import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { NextRequest } from 'next/server';
import { GET } from '@/app/api/social/suggestions/route';

// Mock des dépendances
vi.mock('@/lib/auth', () => ({
  getSession: vi.fn(),
}));

vi.mock('@/lib/db', () => ({
  db: {
    query: {
      users: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
      followers: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
    },
    select: vi.fn(),
  },
}));

import { getSession } from '@/lib/auth';
import { db } from '@/lib/db';

const mockGetSession = vi.mocked(getSession);
const mockDb = vi.mocked(db);

describe('API /api/social/suggestions', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('devrait retourner 401 si l\'utilisateur n\'est pas authentifié', async () => {
    // Arrange
    mockGetSession.mockResolvedValue(null);
    const request = new NextRequest('http://localhost:3000/api/social/suggestions');

    // Act
    const response = await GET(request);
    const data = await response.json();

    // Assert
    expect(response.status).toBe(401);
    expect(data.success).toBe(false);
    expect(data.error).toBe('Non authentifié');
  });

  it('devrait inclure le profil par défaut en première position si pas encore suivi', async () => {
    // Arrange
    const mockUserId = 'test-user-id';
    const mockDefaultUserId = 'default-user-id';
    
    mockGetSession.mockResolvedValue({
      user: { id: mockUserId },
    } as any);

    // Mock du profil par défaut trouvé
    mockDb.query.users.findFirst.mockResolvedValue({
      id: mockDefaultUserId,
      name: 'Simon Gavelle',
      image: 'https://example.com/avatar.jpg',
      publicListId: 'public-123',
    });

    // Mock: l'utilisateur ne suit pas encore le profil par défaut
    mockDb.query.followers.findFirst.mockResolvedValue(null);
    mockDb.query.followers.findMany.mockResolvedValue([]);

    // Mock des suggestions supplémentaires
    const mockSelect = {
      from: vi.fn().mockReturnThis(),
      innerJoin: vi.fn().mockReturnThis(),
      where: vi.fn().mockReturnThis(),
      groupBy: vi.fn().mockReturnThis(),
      orderBy: vi.fn().mockReturnThis(),
      limit: vi.fn().mockResolvedValue([]),
    };
    mockDb.select.mockReturnValue(mockSelect);

    const request = new NextRequest('http://localhost:3000/api/social/suggestions');

    // Act
    const response = await GET(request);
    const data = await response.json();

    // Assert
    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.suggestions).toHaveLength(1);
    expect(data.suggestions[0]).toEqual({
      id: mockDefaultUserId,
      name: 'Simon Gavelle',
      image: 'https://example.com/avatar.jpg',
      publicListId: 'public-123',
      mutualFollowers: 0,
      isDefaultProfile: true,
    });
  });

  it('ne devrait pas inclure le profil par défaut si déjà suivi', async () => {
    // Arrange
    const mockUserId = 'test-user-id';
    const mockDefaultUserId = 'default-user-id';
    
    mockGetSession.mockResolvedValue({
      user: { id: mockUserId },
    } as any);

    // Mock du profil par défaut trouvé
    mockDb.query.users.findFirst.mockResolvedValue({
      id: mockDefaultUserId,
      name: 'Simon Gavelle',
      image: 'https://example.com/avatar.jpg',
      publicListId: 'public-123',
    });

    // Mock: l'utilisateur suit déjà le profil par défaut
    mockDb.query.followers.findFirst.mockResolvedValue({
      followerId: mockUserId,
      followingId: mockDefaultUserId,
    });
    mockDb.query.followers.findMany.mockResolvedValue([]);

    // Mock des suggestions supplémentaires
    const mockSelect = {
      from: vi.fn().mockReturnThis(),
      innerJoin: vi.fn().mockReturnThis(),
      where: vi.fn().mockReturnThis(),
      groupBy: vi.fn().mockReturnThis(),
      orderBy: vi.fn().mockReturnThis(),
      limit: vi.fn().mockResolvedValue([]),
    };
    mockDb.select.mockReturnValue(mockSelect);

    const request = new NextRequest('http://localhost:3000/api/social/suggestions');

    // Act
    const response = await GET(request);
    const data = await response.json();

    // Assert
    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.suggestions).toHaveLength(0);
    expect(data.suggestions.find((s: any) => s.isDefaultProfile)).toBeUndefined();
  });

  it('devrait limiter les suggestions à 10 maximum', async () => {
    // Arrange
    const mockUserId = 'test-user-id';
    
    mockGetSession.mockResolvedValue({
      user: { id: mockUserId },
    } as any);

    // Mock: pas de profil par défaut
    mockDb.query.users.findFirst.mockResolvedValue(null);
    mockDb.query.followers.findMany.mockResolvedValue([]);

    // Mock de 15 suggestions (plus que la limite)
    const mockSuggestions = Array.from({ length: 15 }, (_, i) => ({
      id: `user-${i}`,
      name: `User ${i}`,
      image: null,
      publicListId: `public-${i}`,
      mutualFollowers: 0,
    }));

    const mockSelect = {
      from: vi.fn().mockReturnThis(),
      innerJoin: vi.fn().mockReturnThis(),
      where: vi.fn().mockReturnThis(),
      groupBy: vi.fn().mockReturnThis(),
      orderBy: vi.fn().mockReturnThis(),
      limit: vi.fn().mockResolvedValue(mockSuggestions),
    };
    mockDb.select.mockReturnValue(mockSelect);

    const request = new NextRequest('http://localhost:3000/api/social/suggestions');

    // Act
    const response = await GET(request);
    const data = await response.json();

    // Assert
    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.suggestions).toHaveLength(10); // Limité à 10
  });
});
