"use client";

import { useCallback } from 'react';
import NProgress from 'nprogress';

/**
 * Hook personnalisé pour contrôler la barre de chargement
 * Permet de démarrer/arrêter manuellement la barre de chargement
 */
export function useLoadingBar() {
  const start = useCallback(() => {
    NProgress.start();
  }, []);

  const done = useCallback(() => {
    NProgress.done();
  }, []);

  const set = useCallback((progress: number) => {
    NProgress.set(progress);
  }, []);

  const inc = useCallback((amount?: number) => {
    NProgress.inc(amount);
  }, []);

  return {
    start,
    done,
    set,
    inc,
  };
}
