"use client";

import Image from 'next/image';
import { AlbumArtGrid } from './album-art-grid';
import { cn } from '@/lib/utils';

interface SplashScreenProps {
  isMounted: boolean;
}

export function SplashScreen({ isMounted }: SplashScreenProps) {
  return (
    <div
      className={cn(
        'fixed inset-0 z-[100] flex items-center justify-center bg-gray-900 transition-opacity duration-500',
        isMounted ? 'opacity-100' : 'opacity-0 pointer-events-none'
      )}
    >
      <AlbumArtGrid className="opacity-20" />
      <div className="absolute inset-0 bg-black/50 backdrop-blur-md" />

      <div className="relative z-10 animate-pulse-slow">
        <Image
          src="/stream2spin-logo.svg"
          alt="Stream2Spin Logo"
          width={256}
          height={256}
          priority
        />
      </div>
    </div>
  );
}
