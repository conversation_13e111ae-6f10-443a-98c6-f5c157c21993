"use client";

import Image from 'next/image';
import { cn } from '@/lib/utils';

// Utiliser les 32 images fournies pour une grille riche et variée
const imagePaths = [
  '/cover_art/ab67616d0000b2731216e4f7e84af70ef18146ed.webp',
  '/cover_art/ab67616d0000b2731c0aae998e1c5febd2ee6dca.webp',
  '/cover_art/ab67616d0000b2732f5b249add2cebf2923f8c16.webp',
  '/cover_art/ab67616d0000b2733c182241fcd86aeca2c68a63.webp',
  '/cover_art/ab67616d0000b27343693c2f01ae89613c880ed5.webp',
  '/cover_art/ab67616d0000b273444f118a9126af9e1483dcc0.webp',
  '/cover_art/ab67616d0000b273463d73e65c7b95a17898388b.webp',
  '/cover_art/ab67616d0000b27346bf2c803146e4f89d31ff70.webp',
  '/cover_art/ab67616d0000b2734ccb64f022a53d3e8ec84a20.webp',
  '/cover_art/ab67616d0000b2735b7865be7f7fcc05faec6137.webp',
  '/cover_art/ab67616d0000b273683620d340315866de2ee92d.webp',
  '/cover_art/ab67616d0000b2736d37ac3d3e7079c6ccaa35f6.webp',
  '/cover_art/ab67616d0000b273727318aa69af60549ab3b728.webp',
  '/cover_art/ab67616d0000b2738399047ff71200928f5b6508.webp',
  '/cover_art/ab67616d0000b2739cf15c7323fb85b7112197d5.webp',
  '/cover_art/ab67616d0000b273a1de216c7891e7baa5c79e24.webp',
  '/cover_art/ab67616d0000b273a9249ebb15ca7a5b75f16a90.webp',
  '/cover_art/ab67616d0000b273ada101c2e9e97feb8fae37a9.webp',
  '/cover_art/ab67616d0000b273b11a5489e8cb11dd22b930a0.webp',
  '/cover_art/ab67616d0000b273b1a4724a098cac24abadfa1b.webp',
  '/cover_art/ab67616d0000b273b36949bee43217351961ffbc.webp',
  '/cover_art/ab67616d0000b273b7a9a6a2bf311630d3fc6956.webp',
  '/cover_art/ab67616d0000b273c1a26c0ecbd186049010a5a2.webp',
  '/cover_art/ab67616d0000b273c65b1f9d6ca322de8b85c505.webp',
  '/cover_art/ab67616d0000b273c937b83b0cac925659789137.webp',
  '/cover_art/ab67616d0000b273cb9f9b461b08b21ab93fb130.webp',
  '/cover_art/ab67616d0000b273cbd2ee7dff77bfb2b5f0af52.webp',
  '/cover_art/ab67616d0000b273ced808ef1567eaf901041438.webp',
  '/cover_art/ab67616d0000b273e1782aaa6b4289f27ca0509c.webp',
  '/cover_art/ab67616d0000b273f121e9e68ce75543e7761963.webp',
  '/cover_art/LTk4MzkuanBlZw.webp',
  '/cover_art/NC04MjIzLmpwZWc.webp',
];

// Dupliquer la liste pour assurer un défilement infini et fluide
const imageGrid = [...imagePaths, ...imagePaths];

interface AlbumArtGridProps {
  className?: string;
}

export function AlbumArtGrid({ className }: AlbumArtGridProps) {
  return (
    <div
      className={cn(
        'absolute inset-0 z-0 h-full w-full overflow-hidden',
        className
      )}
    >
      <div className="animate-splash-scroll">
        <div className="grid h-full grid-cols-5 gap-4 p-4">
          {imageGrid.map((src, index) => (
            <div
              key={index}
              className="aspect-square w-full overflow-hidden rounded-lg shadow-lg"
            >
              <Image
                src={src}
                alt={`Album art cover ${index + 1}`}
                width={200}
                height={200}
                className="h-full w-full object-cover"
                priority={true}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
