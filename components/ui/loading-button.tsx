"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useLoadingBar } from '@/hooks/use-loading-bar';
import { cn } from '@/lib/utils';

interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  isLoading?: boolean;
  showLoadingBar?: boolean;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
}

/**
 * Bouton avec gestion automatique de la barre de chargement
 * Exemple d'utilisation du hook useLoadingBar
 */
export function LoadingButton({ 
  children, 
  isLoading = false, 
  showLoadingBar = false,
  onClick,
  disabled,
  className,
  variant = "default",
  size = "default",
  ...props 
}: LoadingButtonProps) {
  const [internalLoading, setInternalLoading] = useState(false);
  const { start, done } = useLoadingBar();

  const handleClick = async (e: React.MouseEvent<HTMLButtonElement>) => {
    if (onClick) {
      if (showLoadingBar) {
        start();
        setInternalLoading(true);
      }

      try {
        await onClick(e);
      } finally {
        if (showLoadingBar) {
          done();
          setInternalLoading(false);
        }
      }
    }
  };

  const loading = isLoading || internalLoading;

  return (
    <Button
      {...props}
      variant={variant}
      size={size}
      onClick={handleClick}
      disabled={disabled || loading}
      className={cn(
        loading && "opacity-70 cursor-not-allowed",
        className
      )}
    >
      {loading ? (
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          {typeof children === 'string' ? 'Chargement...' : children}
        </div>
      ) : (
        children
      )}
    </Button>
  );
}
