"use client";

import { useState, useEffect, createContext, useContext } from 'react';
import { SplashScreen } from '@/components/layout/splash-screen';
import { usePathname } from 'next/navigation';

const SplashScreenContext = createContext<null | boolean>(null);

export function SplashScreenProvider({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const [isMounted, setIsMounted] = useState(true);
  const [isFinished, setIsFinished] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsMounted(false);
    }, 2500); // Splash screen duration

    const finishTimer = setTimeout(() => {
      setIsFinished(true);
    }, 3000); // Duration + transition time

    return () => {
      clearTimeout(timer);
      clearTimeout(finishTimer);
    };
  }, [pathname]); // Reset on route change if needed, though typically runs once

  if (isFinished) {
    return <>{children}</>;
  }

  return (
    <SplashScreenContext.Provider value={isMounted}>
      <SplashScreen isMounted={isMounted} />
      {children}
    </SplashScreenContext.Provider>
  );
}

export const useSplashScreen = () => {
  return useContext(SplashScreenContext);
};
