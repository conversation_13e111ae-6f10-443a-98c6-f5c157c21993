"use client";

import { useEffect, useRef } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import NProgress from 'nprogress';

// Configuration de NProgress
NProgress.configure({
  showSpinner: false, // Désactiver le spinner par défaut
  speed: 400,
  minimum: 0.1,
  easing: 'ease',
  trickleSpeed: 200,
});

interface LoadingBarProviderProps {
  children: React.ReactNode;
}

export function LoadingBarProvider({ children }: LoadingBarProviderProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const isNavigating = useRef(false);

  useEffect(() => {
    // Ne pas démarrer la barre de chargement si on est déjà en train de naviguer
    if (isNavigating.current) {
      return;
    }

    // Démarrer la barre de chargement au début de la navigation
    isNavigating.current = true;
    NProgress.start();

    // Arrêter la barre de chargement une fois que la page est chargée
    const timer = setTimeout(() => {
      NProgress.done();
      isNavigating.current = false;
    }, 150);

    return () => {
      clearTimeout(timer);
      NProgress.done();
      isNavigating.current = false;
    };
  }, [pathname, searchParams]);

  // Gérer les changements de route programmatiques et les liens
  useEffect(() => {
    const handleStart = () => {
      if (!isNavigating.current) {
        isNavigating.current = true;
        NProgress.start();
      }
    };

    const handleComplete = () => {
      setTimeout(() => {
        NProgress.done();
        isNavigating.current = false;
      }, 100);
    };

    // Intercepter les clics sur les liens
    const handleLinkClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a');

      if (link && link.href && !link.href.startsWith('mailto:') && !link.href.startsWith('tel:')) {
        const url = new URL(link.href);
        const currentUrl = new URL(window.location.href);

        // Démarrer la barre de chargement seulement pour les liens internes
        if (url.origin === currentUrl.origin && url.pathname !== currentUrl.pathname) {
          handleStart();
        }
      }
    };

    // Écouter les clics sur les liens
    document.addEventListener('click', handleLinkClick);

    // Écouter les événements popstate (bouton retour)
    window.addEventListener('popstate', handleStart);

    return () => {
      document.removeEventListener('click', handleLinkClick);
      window.removeEventListener('popstate', handleStart);
    };
  }, []);

  return <>{children}</>;
}
