'use client';

import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { User, UserPlus, Crown } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { FollowButton } from './FollowButton';

// Interface pour les suggestions depuis la nouvelle API
interface ProfileSuggestion {
  id: string;
  name: string | null;
  image: string | null;
  publicListId: string | null;
  mutualFollowers: number;
  isDefaultProfile?: boolean;
}

/**
 * Composant carrousel horizontal pour les suggestions de profils
 * Optimisé pour mobile/tablet avec style "stories"
 */
export function ProfileSuggestionsCarousel() {
  const t = useTranslations('Social');
  const [suggestions, setSuggestions] = useState<ProfileSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSuggestions = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/social/suggestions');
        const data = await response.json();
        
        if (data.success) {
          setSuggestions(data.suggestions);
        } else {
          console.error("Erreur API suggestions:", data.error);
        }
      } catch (error) {
        console.error("Erreur lors de la récupération des suggestions:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSuggestions();
  }, []);

  // Fonction pour retirer une suggestion après un follow réussi
  const handleFollowSuccess = (userId: string) => {
    setSuggestions(prev => prev.filter(suggestion => suggestion.id !== userId));
  };

  if (isLoading) {
    return (
      <div className="mb-6">
        <h3 className="font-semibold text-sm mb-3 px-4 flex items-center gap-2">
          <UserPlus className="h-4 w-4" />
          {t('suggestions.title')}
        </h3>
        <div className="flex gap-3 overflow-x-auto px-4 pb-2 scrollbar-hide">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex-shrink-0 w-20">
              <div className="h-16 w-16 bg-muted rounded-full animate-pulse mx-auto mb-2"></div>
              <div className="h-3 w-12 bg-muted rounded-md animate-pulse mx-auto mb-1"></div>
              <div className="h-6 w-16 bg-muted rounded-md animate-pulse mx-auto"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (suggestions.length === 0) {
    return null; // Ne rien afficher si pas de suggestions
  }

  return (
    <div className="mb-6">
      <h3 className="font-semibold text-sm mb-3 px-4 flex items-center gap-2">
        <UserPlus className="h-4 w-4" />
        {t('suggestions.title')}
      </h3>
      <div className="flex gap-3 overflow-x-auto px-4 pb-2 scrollbar-hide">
        {suggestions.map((user) => (
          <div key={user.id} className="flex-shrink-0 w-20 text-center">
            <div className="relative mb-2">
              <Link href={`/u/${user.publicListId}`}>
                <Avatar className="h-16 w-16 mx-auto border-2 border-transparent hover:border-primary/50 transition-colors">
                  <AvatarImage src={user.image || ''} alt={user.name || 'Avatar'} />
                  <AvatarFallback>
                    <User className="h-8 w-8" />
                  </AvatarFallback>
                </Avatar>
              </Link>
              {user.isDefaultProfile && (
                <div className="absolute -top-1 -right-1 bg-yellow-500 rounded-full p-1">
                  <Crown className="h-3 w-3 text-white" />
                </div>
              )}
            </div>
            
            <Link 
              href={`/u/${user.publicListId}`} 
              className="block text-xs font-medium truncate hover:underline mb-1"
              title={user.name || 'Utilisateur'}
            >
              {user.name}
            </Link>
            
            <p className="text-xs text-muted-foreground mb-2 truncate">
              {user.isDefaultProfile 
                ? t('suggestions.founder')
                : user.mutualFollowers > 0 
                  ? `${user.mutualFollowers} commun${user.mutualFollowers > 1 ? 's' : ''}`
                  : t('suggestions.suggestion')}
            </p>
            
            <FollowButton 
              targetUserId={user.id} 
              isFollowingInitial={false} 
              size="sm" 
              className="w-full text-xs px-2 py-1 h-6"
              onFollowAction={() => handleFollowSuccess(user.id)}
            />
          </div>
        ))}
      </div>
    </div>
  );
}
