'use client';

import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { User, UserPlus, Crown } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { FollowButton } from './FollowButton';

// Interface pour les suggestions depuis la nouvelle API
interface ProfileSuggestion {
  id: string;
  name: string | null;
  image: string | null;
  publicListId: string | null;
  mutualFollowers: number;
  isDefaultProfile?: boolean;
}

export function ProfileSuggestions() {
  const t = useTranslations('Social');
  const [suggestions, setSuggestions] = useState<ProfileSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSuggestions = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/social/suggestions');
        const data = await response.json();

        if (data.success) {
          setSuggestions(data.suggestions);
        } else {
          console.error("Erreur API suggestions:", data.error);
        }
      } catch (error) {
        console.error("Erreur lors de la récupération des suggestions:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSuggestions();
  }, []);

  // Fonction pour retirer une suggestion après un follow réussi
  const handleFollowSuccess = (userId: string) => {
    setSuggestions(prev => prev.filter(suggestion => suggestion.id !== userId));
  };

  if (isLoading) {
    return (
      <div className="p-4 border rounded-lg bg-background/50 backdrop-blur-sm">
        <div className="h-4 w-1/2 bg-muted rounded-md animate-pulse mb-4"></div>
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center gap-3">
              <div className="h-10 w-10 bg-muted rounded-full animate-pulse"></div>
              <div className="flex-1 space-y-2">
                <div className="h-3 w-3/4 bg-muted rounded-md animate-pulse"></div>
                <div className="h-3 w-1/2 bg-muted rounded-md animate-pulse"></div>
              </div>
              <div className="h-8 w-16 bg-muted rounded-md animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (suggestions.length === 0) {
    return null; // Ne rien afficher si pas de suggestions
  }

  return (
    <div className="p-4 border rounded-lg bg-background/50 backdrop-blur-sm">
      <h3 className="font-semibold text-sm mb-4 flex items-center gap-2">
        <UserPlus className="h-4 w-4" />
        {t('suggestions.title')}
      </h3>
      <div className="space-y-4">
        {suggestions.map((user) => (
          <div key={user.id} className="flex items-center gap-3">
            <div className="relative">
              <Avatar className="h-10 w-10">
                <AvatarImage src={user.image || ''} alt={user.name || 'Avatar'} />
                <AvatarFallback>
                  <User className="h-5 w-5" />
                </AvatarFallback>
              </Avatar>
              {user.isDefaultProfile && (
                <div className="absolute -top-1 -right-1 bg-yellow-500 rounded-full p-1">
                  <Crown className="h-3 w-3 text-white" />
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <Link href={`/u/${user.publicListId}`} className="font-semibold text-sm truncate hover:underline flex items-center gap-1">
                {user.name}
                {user.isDefaultProfile && (
                  <Crown className="h-3 w-3 text-yellow-500" />
                )}
              </Link>
              <p className="text-xs text-muted-foreground truncate">
                {user.isDefaultProfile
                  ? t('suggestions.founder')
                  : user.mutualFollowers > 0
                    ? t('suggestions.mutualFollowers', { count: user.mutualFollowers })
                    : t('suggestions.suggestion')}
              </p>
            </div>
            <FollowButton
              targetUserId={user.id}
              isFollowingInitial={false}
              size="sm"
              onFollowAction={() => handleFollowSuccess(user.id)}
            />
          </div>
        ))}
      </div>
    </div>
  );
}
