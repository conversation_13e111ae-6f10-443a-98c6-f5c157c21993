"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { LoadingButton } from '@/components/ui/loading-button';
import { useLoadingBar } from '@/hooks/use-loading-bar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

/**
 * Composant de démonstration pour la barre de chargement
 * Montre différentes façons d'utiliser la barre de chargement
 */
export function LoadingBarDemo() {
  const [isLoading, setIsLoading] = useState(false);
  const { start, done, set, inc } = useLoadingBar();

  const simulateLoading = async () => {
    setIsLoading(true);
    start();
    
    // Simulation d'un chargement progressif
    await new Promise(resolve => setTimeout(resolve, 500));
    set(0.3);
    
    await new Promise(resolve => setTimeout(resolve, 500));
    set(0.6);
    
    await new Promise(resolve => setTimeout(resolve, 500));
    set(0.9);
    
    await new Promise(resolve => setTimeout(resolve, 300));
    done();
    setIsLoading(false);
  };

  const simulateQuickLoading = async () => {
    start();
    await new Promise(resolve => setTimeout(resolve, 800));
    done();
  };

  const simulateIncrementalLoading = async () => {
    start();
    
    for (let i = 0; i < 10; i++) {
      await new Promise(resolve => setTimeout(resolve, 200));
      inc(0.1);
    }
    
    done();
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Démonstration de la barre de chargement</CardTitle>
        <CardDescription>
          Testez différents types de chargement avec la barre de progression en haut de page
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Button 
            onClick={simulateLoading}
            disabled={isLoading}
            variant="default"
          >
            {isLoading ? 'Chargement...' : 'Chargement progressif'}
          </Button>
          
          <Button 
            onClick={simulateQuickLoading}
            variant="outline"
          >
            Chargement rapide
          </Button>
          
          <Button 
            onClick={simulateIncrementalLoading}
            variant="secondary"
          >
            Chargement incrémental
          </Button>
          
          <LoadingButton
            showLoadingBar
            onClick={async () => {
              await new Promise(resolve => setTimeout(resolve, 1500));
            }}
            variant="default"
          >
            LoadingButton avec barre
          </LoadingButton>
        </div>
        
        <div className="mt-6 p-4 bg-muted rounded-lg">
          <h4 className="font-semibold mb-2">Comment ça fonctionne :</h4>
          <ul className="text-sm space-y-1 text-muted-foreground">
            <li>• La barre de chargement apparaît automatiquement lors des navigations entre pages</li>
            <li>• Utilisez le hook <code>useLoadingBar()</code> pour contrôler manuellement la barre</li>
            <li>• La couleur principale (#6236FF) est utilisée pour la barre</li>
            <li>• La barre est positionnée en haut de page avec un z-index élevé</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
