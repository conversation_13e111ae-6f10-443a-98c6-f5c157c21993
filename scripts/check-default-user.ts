import { config } from 'dotenv';
config({ path: '.env.local' });

import { db } from '@/lib/db';
import { users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

/**
 * Script pour vérifier l'existence de l'utilisateur par défaut <EMAIL>
 */
async function checkDefaultUser() {
  try {
    console.log('🔍 Recherche de l\'utilisateur <EMAIL>...');

    const defaultUser = await db.query.users.findFirst({
      where: eq(users.email, '<EMAIL>'),
      columns: {
        id: true,
        name: true,
        email: true,
        image: true,
        publicListId: true,
        profileVisibility: true,
        createdAt: true,
      },
    });

    if (defaultUser) {
      console.log('✅ Utilisateur trouvé:');
      console.log('   ID:', defaultUser.id);
      console.log('   Nom:', defaultUser.name);
      console.log('   Email:', defaultUser.email);
      console.log('   Public List ID:', defaultUser.publicListId);
      console.log('   Visibilité:', defaultUser.profileVisibility);
      console.log('   Créé le:', defaultUser.createdAt);
      
      // Vérifier si le profil a un publicListId
      if (!defaultUser.publicListId) {
        console.log('⚠️  ATTENTION: Le profil n\'a pas de publicListId');
      }
      
      // Vérifier la visibilité du profil
      if (defaultUser.profileVisibility === 'private') {
        console.log('⚠️  ATTENTION: Le profil est privé');
      }
      
    } else {
      console.log('❌ Utilisateur <EMAIL> non trouvé dans la base de données');
      console.log('💡 Suggestions:');
      console.log('   1. Vérifier que l\'utilisateur s\'est connecté au moins une fois');
      console.log('   2. Vérifier l\'orthographe de l\'email');
      console.log('   3. Créer le compte si nécessaire');
    }

    // Afficher quelques utilisateurs existants pour référence
    console.log('\n📋 Quelques utilisateurs existants:');
    const existingUsers = await db.query.users.findMany({
      columns: {
        id: true,
        name: true,
        email: true,
        publicListId: true,
      },
      limit: 5,
    });
    
    existingUsers.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.name} (${user.email}) - ID: ${user.id}`);
    });

  } catch (error) {
    console.error('❌ Erreur lors de la vérification:', error);
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  checkDefaultUser()
    .then(() => {
      console.log('\n✅ Vérification terminée');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erreur fatale:', error);
      process.exit(1);
    });
}

export { checkDefaultUser };
