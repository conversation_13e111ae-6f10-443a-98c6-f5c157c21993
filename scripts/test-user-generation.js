#!/usr/bin/env node

/**
 * Script pour tester la génération de recommandations via l'API
 * Usage: node scripts/test-user-generation.js [userId]
 */

const PROBLEMATIC_USER_ID = "98fe2c93-84ff-4a2c-b1a0-dab1c38881b2";
const BASE_URL = "https://app.stream2spin.com";

async function testUserGeneration(userId = PROBLEMATIC_USER_ID) {
  console.log(`🔍 Test de génération pour l'utilisateur: ${userId}`);
  console.log("=".repeat(60));

  try {
    // 1. Tester l'API de génération manuelle
    console.log("\n1. 🎵 Test de l'API de génération manuelle...");

    const generateResponse = await fetch(`${BASE_URL}/api/generate-recommendations-now?userId=${userId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const generateResult = await generateResponse.json();
    
    if (generateResponse.ok) {
      console.log("✅ Génération réussie:");
      console.log(`   Status: ${generateResult.result?.status}`);
      console.log(`   Recommandations: ${generateResult.result?.recommendationsCount || 0}`);
    } else {
      console.log("❌ Erreur de génération:");
      console.log(`   Status: ${generateResponse.status}`);
      console.log(`   Error: ${generateResult.error}`);
      console.log(`   Message: ${generateResult.message}`);
    }

    // 2. Tester l'API de stream de génération
    console.log("\n2. 📡 Test de l'API de stream de génération...");
    
    const streamResponse = await fetch(`${BASE_URL}/api/generation-stream?userId=${userId}&lang=fr`, {
      method: 'GET',
    });

    if (streamResponse.ok) {
      console.log("✅ Stream de génération accessible");
      
      // Lire le début du stream
      const reader = streamResponse.body?.getReader();
      if (reader) {
        let chunks = 0;
        let lastMessage = "";
        
        try {
          while (chunks < 10) { // Lire seulement les 10 premiers chunks
            const { done, value } = await reader.read();
            if (done) break;
            
            const text = new TextDecoder().decode(value);
            if (text.includes('data:')) {
              lastMessage = text;
              chunks++;
            }
          }
          
          console.log(`   Chunks reçus: ${chunks}`);
          console.log(`   Dernier message: ${lastMessage.substring(0, 100)}...`);
          
        } catch (streamError) {
          console.log(`   ⚠️ Erreur de lecture du stream: ${streamError.message}`);
        } finally {
          reader.releaseLock();
        }
      }
    } else {
      console.log("❌ Erreur du stream de génération:");
      console.log(`   Status: ${streamResponse.status}`);
      const errorText = await streamResponse.text();
      console.log(`   Error: ${errorText.substring(0, 200)}...`);
    }

    // 3. Vérifier les recommandations existantes
    console.log("\n3. 📊 Vérification des recommandations existantes...");
    
    const timeframes = ['short_term', 'medium_term', 'long_term'];
    
    for (const timeframe of timeframes) {
      try {
        const recsResponse = await fetch(`${BASE_URL}/api/recommendations/filtered?timeframe=${timeframe}`, {
          method: 'GET',
          headers: {
            'Cookie': `userId=${userId}`, // Simulation d'une session
          },
        });

        if (recsResponse.ok) {
          const recsData = await recsResponse.json();
          console.log(`   ${timeframe}: ${recsData.recommendations?.length || 0} recommandations`);
        } else {
          console.log(`   ${timeframe}: Erreur ${recsResponse.status}`);
        }
      } catch (timeframeError) {
        console.log(`   ${timeframe}: Erreur ${timeframeError.message}`);
      }
    }

    console.log("\n✅ Test terminé");

  } catch (error) {
    console.error(`❌ Erreur lors du test: ${error.message}`);
  }
}

// Exécuter le test
const userId = process.argv[2] || PROBLEMATIC_USER_ID;
testUserGeneration(userId).then(() => {
  console.log("\n🏁 Test terminé");
}).catch((error) => {
  console.error("💥 Erreur fatale:", error);
});
