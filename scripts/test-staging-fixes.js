#!/usr/bin/env node

/**
 * Script pour tester les corrections sur l'environnement staging
 * Usage: node scripts/test-staging-fixes.js
 */

const STAGING_URL = "https://stream2spin-staging.vercel.app";

async function testStagingFixes() {
  console.log("🧪 Test des corrections sur STAGING");
  console.log("URL:", STAGING_URL);
  console.log("=".repeat(60));

  // 1. Tester que la table analytics existe en staging
  console.log("\n1. 📊 Test de la table analytics en staging...");
  try {
    // Créer la table analytics en staging si elle n'existe pas
    const migrateResponse = await fetch(`${STAGING_URL}/api/migrate/add-analytics-table`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const migrateResult = await migrateResponse.json();
    
    if (migrateResponse.ok) {
      console.log("✅ Migration analytics staging réussie");
      console.log(`   Message: ${migrateResult.message}`);
    } else {
      console.log(`⚠️  Migration analytics staging: ${migrateResponse.status}`);
      console.log(`   Error: ${migrateResult.error || 'Unknown'}`);
    }
  } catch (error) {
    console.log(`❌ Erreur migration staging: ${error.message}`);
  }

  // 2. Tester une page publique en staging
  console.log("\n2. 🌐 Test des pages publiques staging...");
  try {
    // Tester avec un ID générique (peut retourner 404 mais ne doit pas crasher)
    const publicPageResponse = await fetch(`${STAGING_URL}/u/test-public-id`, {
      method: 'GET',
    });

    console.log(`   Status: ${publicPageResponse.status}`);
    
    if (publicPageResponse.status === 404) {
      console.log("✅ Page publique retourne 404 (normal pour ID inexistant)");
    } else if (publicPageResponse.status === 200) {
      console.log("✅ Page publique accessible");
    } else if (publicPageResponse.status >= 500) {
      console.log("❌ Erreur serveur sur page publique");
      const errorText = await publicPageResponse.text();
      if (errorText.includes('public_list_analytics')) {
        console.log("❌ Table analytics toujours manquante en staging");
      }
    }
  } catch (error) {
    console.log(`❌ Erreur test page publique staging: ${error.message}`);
  }

  // 3. Tester l'API de génération en staging
  console.log("\n3. 🎵 Test API génération staging...");
  try {
    const generateResponse = await fetch(`${STAGING_URL}/api/generate-recommendations-now?userId=test-user-id`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const generateResult = await generateResponse.json();
    
    if (generateResponse.ok) {
      console.log("✅ API génération staging accessible");
      console.log(`   Status: ${generateResult.result?.status || 'unknown'}`);
    } else {
      console.log(`⚠️  API génération staging: ${generateResponse.status}`);
      console.log(`   Error: ${generateResult.error || 'Unknown'}`);
    }
  } catch (error) {
    console.log(`❌ Erreur test API génération staging: ${error.message}`);
  }

  // 4. Vérifier le déploiement
  console.log("\n4. 🚀 Vérification du déploiement staging...");
  try {
    const healthResponse = await fetch(`${STAGING_URL}/api/health`, {
      method: 'GET',
    });

    if (healthResponse.ok) {
      console.log("✅ Staging déployé et accessible");
    } else {
      console.log(`⚠️  Staging health check: ${healthResponse.status}`);
    }
  } catch (error) {
    console.log(`❌ Staging non accessible: ${error.message}`);
  }

  console.log("\n📋 Résumé du test staging:");
  console.log("   🔧 Migration analytics exécutée");
  console.log("   🌐 Pages publiques testées");
  console.log("   🎵 API génération testée");
  console.log("   🚀 Déploiement vérifié");
  
  console.log("\n🎯 Prochaines étapes:");
  console.log("   1. Vérifier le dashboard Vercel pour le déploiement");
  console.log("   2. Tester manuellement sur https://stream2spin-staging.vercel.app");
  console.log("   3. Valider les corrections avant déploiement production");
  
  console.log("\n✅ Tests staging terminés");
}

// Exécuter les tests
testStagingFixes().then(() => {
  console.log("\n🏁 Tests staging terminés");
}).catch((error) => {
  console.error("💥 Erreur fatale:", error);
});
