#!/usr/bin/env tsx

/**
 * Script de diagnostic pour analyser les problèmes de génération de recommandations
 * Usage: npx tsx scripts/diagnose-user-generation.ts [userId]
 */

import { db } from "../lib/db";
import { users, accounts, recommendations } from "../lib/db/schema";
import { eq, and, desc } from "drizzle-orm";
import { getValidSpotifyToken, fetchUserTopTracks, analyzeTracksAndCalculateScores } from "../lib/spotify";

const PROBLEMATIC_USER_ID = "98fe2c93-84ff-4a2c-b1a0-dab1c38881b2";

async function diagnoseUser(userId: string = PROBLEMATIC_USER_ID) {
  console.log(`🔍 Diagnostic pour l'utilisateur: ${userId}`);
  console.log("=".repeat(60));

  try {
    // 1. Vérifier que l'utilisateur existe
    console.log("\n1. 👤 Vérification de l'utilisateur...");
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: {
        id: true,
        name: true,
        email: true,
        createdAt: true,
      },
    });

    if (!user) {
      console.log("❌ Utilisateur non trouvé");
      return;
    }

    console.log(`✅ Utilisateur trouvé: ${user.name || user.email}`);
    console.log(`   Créé le: ${user.createdAt}`);

    // 2. Vérifier les comptes OAuth
    console.log("\n2. 🔑 Vérification des comptes OAuth...");
    const spotifyAccount = await db.query.accounts.findFirst({
      where: and(
        eq(accounts.userId, userId),
        eq(accounts.provider, "spotify")
      ),
    });

    if (!spotifyAccount) {
      console.log("❌ Aucun compte Spotify trouvé");
      return;
    }

    console.log("✅ Compte Spotify trouvé");
    console.log(`   Provider Account ID: ${spotifyAccount.providerAccountId}`);
    console.log(`   Token expires: ${spotifyAccount.expires_at ? new Date(spotifyAccount.expires_at * 1000) : 'N/A'}`);

    // 3. Tester le token Spotify
    console.log("\n3. 🎵 Test du token Spotify...");
    try {
      const validToken = await getValidSpotifyToken(userId);
      if (!validToken) {
        console.log("❌ Impossible d'obtenir un token Spotify valide");
        return;
      }
      console.log("✅ Token Spotify valide obtenu");
    } catch (tokenError) {
      console.log(`❌ Erreur token Spotify: ${tokenError}`);
      return;
    }

    // 4. Tester les top tracks pour chaque timeframe
    console.log("\n4. 📊 Test des top tracks...");
    const timeframes = ['short_term', 'medium_term', 'long_term'] as const;
    const validToken = await getValidSpotifyToken(userId);

    for (const timeframe of timeframes) {
      try {
        console.log(`\n   Testing ${timeframe}...`);
        const topTracks = await fetchUserTopTracks(validToken!, timeframe);
        
        if (!topTracks || topTracks.length === 0) {
          console.log(`   ⚠️ Aucun top track pour ${timeframe}`);
          continue;
        }

        console.log(`   ✅ ${topTracks.length} top tracks trouvés pour ${timeframe}`);
        
        // Analyser les recommandations
        const recommendations = analyzeTracksAndCalculateScores(topTracks, timeframe);
        console.log(`   📀 ${recommendations.length} recommandations générées`);

        // Afficher quelques exemples
        if (recommendations.length > 0) {
          console.log("   Top 3 recommandations:");
          recommendations.slice(0, 3).forEach((rec, index) => {
            console.log(`     ${index + 1}. ${rec.artistName} - ${rec.albumTitle} (Score: ${rec.listenScore})`);
          });
        }

      } catch (timeframeError) {
        console.log(`   ❌ Erreur pour ${timeframe}: ${timeframeError}`);
      }
    }

    // 5. Vérifier les recommandations existantes en base
    console.log("\n5. 💾 Vérification des recommandations en base...");
    for (const timeframe of timeframes) {
      const existingRecs = await db.query.recommendations.findMany({
        where: and(
          eq(recommendations.userId, userId),
          eq(recommendations.timeframe, timeframe)
        ),
        orderBy: [desc(recommendations.generatedAt)],
        limit: 5,
      });

      console.log(`   ${timeframe}: ${existingRecs.length} recommandations en base`);
      if (existingRecs.length > 0) {
        const latest = existingRecs[0];
        console.log(`     Dernière génération: ${latest.generatedAt}`);
      }
    }

    console.log("\n✅ Diagnostic terminé");

  } catch (error) {
    console.error(`❌ Erreur lors du diagnostic: ${error}`);
  }
}

// Exécuter le diagnostic
const userId = process.argv[2] || PROBLEMATIC_USER_ID;
diagnoseUser(userId).then(() => {
  console.log("\n🏁 Diagnostic terminé");
  process.exit(0);
}).catch((error) => {
  console.error("💥 Erreur fatale:", error);
  process.exit(1);
});
