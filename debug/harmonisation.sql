-- #####################################################################
-- ##               SCRIPT DE MIGRATION : PROD -> STAGING             ##
-- #####################################################################
-- ATTENTION: Ce script modifie la structure de la base de données.
-- À utiliser avec précaution. Faites une sauvegarde avant de l'exécuter.
-- Pour plus de sécurité, enveloppez ce script dans une transaction 
-- (BEGIN; ... COMMIT;/ROLLBACK;).
-- #####################################################################

BEGIN;

-- =====================================================================
-- 1. Création des tables manquantes en production
-- =====================================================================

-- Table pour les métriques de déliverabilité des e-mails
CREATE TABLE IF NOT EXISTS public.email_deliverability_metrics (
    id SERIAL PRIMARY KEY,
    date DATE NOT NULL,
    emails_sent INTEGER DEFAULT 0,
    emails_delivered INTEGER DEFAULT 0,
    emails_bounced INTEGER DEFAULT 0,
    emails_complained INTEGER DEFAULT 0,
    emails_opened INTEGER DEFAULT 0,
    emails_clicked INTEGER DEFAULT 0,
    deliverability_rate NUMERIC(5, 2),
    open_rate NUMERIC(5, 2),
    click_rate NUMERIC(5, 2),
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT now()
);

-- Table pour la liste noire des domaines d'e-mails
CREATE TABLE IF NOT EXISTS public.email_domain_blacklist (
    id SERIAL PRIMARY KEY,
    domain TEXT NOT NULL UNIQUE,
    reason TEXT,
    added_at TIMESTAMP WITHOUT TIME ZONE DEFAULT now(),
    is_active BOOLEAN DEFAULT true
);

-- Table pour les événements liés aux e-mails
CREATE TABLE IF NOT EXISTS public.email_events (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    email_id TEXT NOT NULL,
    event_type TEXT NOT NULL,
    event_data JSONB,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT now()
);


-- =====================================================================
-- 2. Ajustement des tables et colonnes existantes
-- =====================================================================

-- Table: public.recommendations
-- ATTENTION : Changer BIGINT en INTEGER peut échouer si des IDs dépassent la limite de l'INTEGER.
-- Vérifiez la valeur max de l'ID avant : SELECT MAX(id) FROM public.recommendations;
ALTER TABLE public.recommendations
    ALTER COLUMN id TYPE INTEGER,
    ADD COLUMN IF NOT EXISTS "isWishlisted" BOOLEAN DEFAULT false,
    ALTER COLUMN "generatedAt" TYPE TIMESTAMP WITHOUT TIME ZONE;

-- Table: public.sessions
ALTER TABLE public.sessions
    ALTER COLUMN expires TYPE TIMESTAMP WITHOUT TIME ZONE;

-- Table: public.user_discogs_collection
-- ATTENTION : Changer BIGINT en INTEGER peut échouer si des IDs dépassent la limite de l'INTEGER.
-- Vérifiez la valeur max de l'ID avant : SELECT MAX(id) FROM public.user_discogs_collection;
ALTER TABLE public.user_discogs_collection
    ALTER COLUMN id TYPE INTEGER,
    ALTER COLUMN "syncedAt" TYPE TIMESTAMP WITHOUT TIME ZONE;

-- Table: public.user_fcm_tokens
-- ATTENTION : Changer BIGINT en INTEGER peut échouer si des IDs dépassent la limite de l'INTEGER.
-- Vérifiez la valeur max de l'ID avant : SELECT MAX(id) FROM public.user_fcm_tokens;
ALTER TABLE public.user_fcm_tokens
    ALTER COLUMN id TYPE INTEGER;
-- Renommage de la colonne
ALTER TABLE public.user_fcm_tokens RENAME COLUMN "lastUsedAt" TO "lastUsed";
-- Modification des types de timestamp
ALTER TABLE public.user_fcm_tokens
    ALTER COLUMN "createdAt" TYPE TIMESTAMP WITHOUT TIME ZONE,
    ALTER COLUMN "lastUsed" TYPE TIMESTAMP WITHOUT TIME ZONE;

-- Table: public.users
ALTER TABLE public.users
    ALTER COLUMN "emailVerified" TYPE TIMESTAMP WITHOUT TIME ZONE;

-- Table: public.verificationTokens
-- Renommage de la table pour correspondre à staging
ALTER TABLE public.verificationTokens RENAME TO verification_tokens;
ALTER TABLE public.verification_tokens
    ALTER COLUMN expires TYPE TIMESTAMP WITHOUT TIME ZONE;

-- Table: public.wishlist_items
-- Ajout d'une clé primaire auto-incrémentée
ALTER TABLE public.wishlist_items
    ADD COLUMN IF NOT EXISTS id SERIAL PRIMARY KEY,
    ALTER COLUMN "createdAt" TYPE TIMESTAMP WITHOUT TIME ZONE;


-- =====================================================================
-- 3. Fin de la migration
-- =====================================================================

-- Si tout s'est bien passé, exécutez COMMIT;
-- En cas de problème, exécutez ROLLBACK;

COMMIT;
