table_schema,table_name,column_name,data_type,character_maximum_length,numeric_precision,numeric_scale,is_nullable,column_default,ordinal_position
auth,audit_log_entries,instance_id,uuid,null,null,null,YES,null,1
auth,audit_log_entries,id,uuid,null,null,null,NO,null,2
auth,audit_log_entries,payload,json,null,null,null,YES,null,3
auth,audit_log_entries,created_at,timestamp with time zone,null,null,null,YES,null,4
auth,audit_log_entries,ip_address,character varying,64,null,null,NO,''::character varying,5
auth,flow_state,id,uuid,null,null,null,NO,null,1
auth,flow_state,user_id,uuid,null,null,null,YES,null,2
auth,flow_state,auth_code,text,null,null,null,NO,null,3
auth,flow_state,code_challenge_method,USER-DEFINED,null,null,null,NO,null,4
auth,flow_state,code_challenge,text,null,null,null,NO,null,5
auth,flow_state,provider_type,text,null,null,null,NO,null,6
auth,flow_state,provider_access_token,text,null,null,null,YES,null,7
auth,flow_state,provider_refresh_token,text,null,null,null,YES,null,8
auth,flow_state,created_at,timestamp with time zone,null,null,null,YES,null,9
auth,flow_state,updated_at,timestamp with time zone,null,null,null,YES,null,10
auth,flow_state,authentication_method,text,null,null,null,NO,null,11
auth,flow_state,auth_code_issued_at,timestamp with time zone,null,null,null,YES,null,12
auth,identities,provider_id,text,null,null,null,NO,null,1
auth,identities,user_id,uuid,null,null,null,NO,null,2
auth,identities,identity_data,jsonb,null,null,null,NO,null,3
auth,identities,provider,text,null,null,null,NO,null,4
auth,identities,last_sign_in_at,timestamp with time zone,null,null,null,YES,null,5
auth,identities,created_at,timestamp with time zone,null,null,null,YES,null,6
auth,identities,updated_at,timestamp with time zone,null,null,null,YES,null,7
auth,identities,email,text,null,null,null,YES,null,8
auth,identities,id,uuid,null,null,null,NO,gen_random_uuid(),9
auth,instances,id,uuid,null,null,null,NO,null,1
auth,instances,uuid,uuid,null,null,null,YES,null,2
auth,instances,raw_base_config,text,null,null,null,YES,null,3
auth,instances,created_at,timestamp with time zone,null,null,null,YES,null,4
auth,instances,updated_at,timestamp with time zone,null,null,null,YES,null,5
auth,mfa_amr_claims,session_id,uuid,null,null,null,NO,null,1
auth,mfa_amr_claims,created_at,timestamp with time zone,null,null,null,NO,null,2
auth,mfa_amr_claims,updated_at,timestamp with time zone,null,null,null,NO,null,3
auth,mfa_amr_claims,authentication_method,text,null,null,null,NO,null,4
auth,mfa_amr_claims,id,uuid,null,null,null,NO,null,5
auth,mfa_challenges,id,uuid,null,null,null,NO,null,1
auth,mfa_challenges,factor_id,uuid,null,null,null,NO,null,2
auth,mfa_challenges,created_at,timestamp with time zone,null,null,null,NO,null,3
auth,mfa_challenges,verified_at,timestamp with time zone,null,null,null,YES,null,4
auth,mfa_challenges,ip_address,inet,null,null,null,NO,null,5
auth,mfa_challenges,otp_code,text,null,null,null,YES,null,6
auth,mfa_challenges,web_authn_session_data,jsonb,null,null,null,YES,null,7
auth,mfa_factors,id,uuid,null,null,null,NO,null,1
auth,mfa_factors,user_id,uuid,null,null,null,NO,null,2
auth,mfa_factors,friendly_name,text,null,null,null,YES,null,3
auth,mfa_factors,factor_type,USER-DEFINED,null,null,null,NO,null,4
auth,mfa_factors,status,USER-DEFINED,null,null,null,NO,null,5
auth,mfa_factors,created_at,timestamp with time zone,null,null,null,NO,null,6
auth,mfa_factors,updated_at,timestamp with time zone,null,null,null,NO,null,7
auth,mfa_factors,secret,text,null,null,null,YES,null,8
auth,mfa_factors,phone,text,null,null,null,YES,null,9
auth,mfa_factors,last_challenged_at,timestamp with time zone,null,null,null,YES,null,10
auth,mfa_factors,web_authn_credential,jsonb,null,null,null,YES,null,11
auth,mfa_factors,web_authn_aaguid,uuid,null,null,null,YES,null,12
auth,one_time_tokens,id,uuid,null,null,null,NO,null,1
auth,one_time_tokens,user_id,uuid,null,null,null,NO,null,2
auth,one_time_tokens,token_type,USER-DEFINED,null,null,null,NO,null,3
auth,one_time_tokens,token_hash,text,null,null,null,NO,null,4
auth,one_time_tokens,relates_to,text,null,null,null,NO,null,5
auth,one_time_tokens,created_at,timestamp without time zone,null,null,null,NO,now(),6
auth,one_time_tokens,updated_at,timestamp without time zone,null,null,null,NO,now(),7
auth,refresh_tokens,instance_id,uuid,null,null,null,YES,null,1
auth,refresh_tokens,id,bigint,null,64,0,NO,nextval('auth.refresh_tokens_id_seq'::regclass),2
auth,refresh_tokens,token,character varying,255,null,null,YES,null,3
auth,refresh_tokens,user_id,character varying,255,null,null,YES,null,4
auth,refresh_tokens,revoked,boolean,null,null,null,YES,null,5
auth,refresh_tokens,created_at,timestamp with time zone,null,null,null,YES,null,6
auth,refresh_tokens,updated_at,timestamp with time zone,null,null,null,YES,null,7
auth,refresh_tokens,parent,character varying,255,null,null,YES,null,8
auth,refresh_tokens,session_id,uuid,null,null,null,YES,null,9
auth,saml_providers,id,uuid,null,null,null,NO,null,1
auth,saml_providers,sso_provider_id,uuid,null,null,null,NO,null,2
auth,saml_providers,entity_id,text,null,null,null,NO,null,3
auth,saml_providers,metadata_xml,text,null,null,null,NO,null,4
auth,saml_providers,metadata_url,text,null,null,null,YES,null,5
auth,saml_providers,attribute_mapping,jsonb,null,null,null,YES,null,6
auth,saml_providers,created_at,timestamp with time zone,null,null,null,YES,null,7
auth,saml_providers,updated_at,timestamp with time zone,null,null,null,YES,null,8
auth,saml_providers,name_id_format,text,null,null,null,YES,null,9
auth,saml_relay_states,id,uuid,null,null,null,NO,null,1
auth,saml_relay_states,sso_provider_id,uuid,null,null,null,NO,null,2
auth,saml_relay_states,request_id,text,null,null,null,NO,null,3
auth,saml_relay_states,for_email,text,null,null,null,YES,null,4
auth,saml_relay_states,redirect_to,text,null,null,null,YES,null,5
auth,saml_relay_states,created_at,timestamp with time zone,null,null,null,YES,null,7
auth,saml_relay_states,updated_at,timestamp with time zone,null,null,null,YES,null,8
auth,saml_relay_states,flow_state_id,uuid,null,null,null,YES,null,9
auth,schema_migrations,version,character varying,255,null,null,NO,null,1
auth,sessions,id,uuid,null,null,null,NO,null,1
auth,sessions,user_id,uuid,null,null,null,NO,null,2
auth,sessions,created_at,timestamp with time zone,null,null,null,YES,null,3
auth,sessions,updated_at,timestamp with time zone,null,null,null,YES,null,4
auth,sessions,factor_id,uuid,null,null,null,YES,null,5
auth,sessions,aal,USER-DEFINED,null,null,null,YES,null,6
auth,sessions,not_after,timestamp with time zone,null,null,null,YES,null,7
auth,sessions,refreshed_at,timestamp without time zone,null,null,null,YES,null,8
auth,sessions,user_agent,text,null,null,null,YES,null,9
auth,sessions,ip,inet,null,null,null,YES,null,10
auth,sessions,tag,text,null,null,null,YES,null,11
auth,sso_domains,id,uuid,null,null,null,NO,null,1
auth,sso_domains,sso_provider_id,uuid,null,null,null,NO,null,2
auth,sso_domains,domain,text,null,null,null,NO,null,3
auth,sso_domains,created_at,timestamp with time zone,null,null,null,YES,null,4
auth,sso_domains,updated_at,timestamp with time zone,null,null,null,YES,null,5
auth,sso_providers,id,uuid,null,null,null,NO,null,1
auth,sso_providers,resource_id,text,null,null,null,YES,null,2
auth,sso_providers,created_at,timestamp with time zone,null,null,null,YES,null,3
auth,sso_providers,updated_at,timestamp with time zone,null,null,null,YES,null,4
auth,users,instance_id,uuid,null,null,null,YES,null,1
auth,users,id,uuid,null,null,null,NO,null,2
auth,users,aud,character varying,255,null,null,YES,null,3
auth,users,role,character varying,255,null,null,YES,null,4
auth,users,email,character varying,255,null,null,YES,null,5
auth,users,encrypted_password,character varying,255,null,null,YES,null,6
auth,users,email_confirmed_at,timestamp with time zone,null,null,null,YES,null,7
auth,users,invited_at,timestamp with time zone,null,null,null,YES,null,8
auth,users,confirmation_token,character varying,255,null,null,YES,null,9
auth,users,confirmation_sent_at,timestamp with time zone,null,null,null,YES,null,10
auth,users,recovery_token,character varying,255,null,null,YES,null,11
auth,users,recovery_sent_at,timestamp with time zone,null,null,null,YES,null,12
auth,users,email_change_token_new,character varying,255,null,null,YES,null,13
auth,users,email_change,character varying,255,null,null,YES,null,14
auth,users,email_change_sent_at,timestamp with time zone,null,null,null,YES,null,15
auth,users,last_sign_in_at,timestamp with time zone,null,null,null,YES,null,16
auth,users,raw_app_meta_data,jsonb,null,null,null,YES,null,17
auth,users,raw_user_meta_data,jsonb,null,null,null,YES,null,18
auth,users,is_super_admin,boolean,null,null,null,YES,null,19
auth,users,created_at,timestamp with time zone,null,null,null,YES,null,20
auth,users,updated_at,timestamp with time zone,null,null,null,YES,null,21
auth,users,phone,text,null,null,null,YES,NULL::character varying,22
auth,users,phone_confirmed_at,timestamp with time zone,null,null,null,YES,null,23
auth,users,phone_change,text,null,null,null,YES,''::character varying,24
auth,users,phone_change_token,character varying,255,null,null,YES,''::character varying,25
auth,users,phone_change_sent_at,timestamp with time zone,null,null,null,YES,null,26
auth,users,confirmed_at,timestamp with time zone,null,null,null,YES,null,27
auth,users,email_change_token_current,character varying,255,null,null,YES,''::character varying,28
auth,users,email_change_confirm_status,smallint,null,16,0,YES,0,29
auth,users,banned_until,timestamp with time zone,null,null,null,YES,null,30
auth,users,reauthentication_token,character varying,255,null,null,YES,''::character varying,31
auth,users,reauthentication_sent_at,timestamp with time zone,null,null,null,YES,null,32
auth,users,is_sso_user,boolean,null,null,null,NO,false,33
auth,users,deleted_at,timestamp with time zone,null,null,null,YES,null,34
auth,users,is_anonymous,boolean,null,null,null,NO,false,35
public,accounts,userId,text,null,null,null,NO,null,1
public,accounts,type,text,null,null,null,NO,null,2
public,accounts,provider,text,null,null,null,NO,null,3
public,accounts,providerAccountId,text,null,null,null,NO,null,4
public,accounts,refresh_token,text,null,null,null,YES,null,5
public,accounts,access_token,text,null,null,null,YES,null,6
public,accounts,access_token_secret,text,null,null,null,YES,null,7
public,accounts,expires_at,integer,null,32,0,YES,null,8
public,accounts,token_type,text,null,null,null,YES,null,9
public,accounts,scope,text,null,null,null,YES,null,10
public,accounts,id_token,text,null,null,null,YES,null,11
public,accounts,session_state,text,null,null,null,YES,null,12
public,email_deliverability_metrics,id,integer,null,32,0,NO,nextval('email_deliverability_metrics_id_seq'::regclass),1
public,email_deliverability_metrics,date,date,null,null,null,NO,null,2
public,email_deliverability_metrics,emails_sent,integer,null,32,0,YES,0,3
public,email_deliverability_metrics,emails_delivered,integer,null,32,0,YES,0,4
public,email_deliverability_metrics,emails_bounced,integer,null,32,0,YES,0,5
public,email_deliverability_metrics,emails_complained,integer,null,32,0,YES,0,6
public,email_deliverability_metrics,emails_opened,integer,null,32,0,YES,0,7
public,email_deliverability_metrics,emails_clicked,integer,null,32,0,YES,0,8
public,email_deliverability_metrics,deliverability_rate,numeric,null,5,2,YES,null,9
public,email_deliverability_metrics,open_rate,numeric,null,5,2,YES,null,10
public,email_deliverability_metrics,click_rate,numeric,null,5,2,YES,null,11
public,email_deliverability_metrics,created_at,timestamp without time zone,null,null,null,YES,now(),12
public,email_domain_blacklist,id,integer,null,32,0,NO,nextval('email_domain_blacklist_id_seq'::regclass),1
public,email_domain_blacklist,domain,text,null,null,null,NO,null,2
public,email_domain_blacklist,reason,text,null,null,null,YES,null,3
public,email_domain_blacklist,added_at,timestamp without time zone,null,null,null,YES,now(),4
public,email_domain_blacklist,is_active,boolean,null,null,null,YES,true,5
public,email_events,id,integer,null,32,0,NO,nextval('email_events_id_seq'::regclass),1
public,email_events,user_id,text,null,null,null,NO,null,2
public,email_events,email_id,text,null,null,null,NO,null,3
public,email_events,event_type,text,null,null,null,NO,null,4
public,email_events,event_data,jsonb,null,null,null,YES,null,5
public,email_events,created_at,timestamp without time zone,null,null,null,YES,now(),6
public,followers,follower_id,text,null,null,null,NO,null,1
public,followers,following_id,text,null,null,null,NO,null,2
public,followers,created_at,timestamp without time zone,null,null,null,NO,now(),3
public,notifications,id,text,null,null,null,NO,gen_random_uuid(),1
public,notifications,recipient_id,text,null,null,null,NO,null,2
public,notifications,actor_id,text,null,null,null,NO,null,3
public,notifications,type,USER-DEFINED,null,null,null,NO,null,4
public,notifications,is_read,boolean,null,null,null,NO,false,5
public,notifications,created_at,timestamp without time zone,null,null,null,NO,now(),6
public,recommendations,id,integer,null,32,0,NO,nextval('recommendations_id_seq'::regclass),1
public,recommendations,userId,text,null,null,null,NO,null,2
public,recommendations,artistName,text,null,null,null,NO,null,3
public,recommendations,albumTitle,text,null,null,null,NO,null,4
public,recommendations,albumCoverUrl,text,null,null,null,YES,null,5
public,recommendations,discogsReleaseId,bigint,null,64,0,YES,null,6
public,recommendations,spotifyAlbumId,text,null,null,null,YES,null,7
public,recommendations,listenScore,integer,null,32,0,NO,null,8
public,recommendations,estimatedPlays,integer,null,32,0,YES,0,9
public,recommendations,isOwned,boolean,null,null,null,YES,false,10
public,recommendations,isWishlisted,boolean,null,null,null,YES,false,11
public,recommendations,affiliateLinks,jsonb,null,null,null,YES,null,12
public,recommendations,generatedAt,timestamp without time zone,null,null,null,YES,now(),13
public,recommendations,timeframe,text,null,null,null,NO,null,14
public,recommendations,topTrackName,text,null,null,null,YES,null,15
public,recommendations,topTrackId,text,null,null,null,YES,null,16
public,recommendations,topTrackPreviewUrl,text,null,null,null,YES,null,17
public,recommendations,topTrackListenScore,integer,null,32,0,YES,null,18
public,sessions,sessionToken,text,null,null,null,NO,null,1
public,sessions,userId,text,null,null,null,NO,null,2
public,sessions,expires,timestamp without time zone,null,null,null,NO,null,3
public,user_discogs_collection,id,integer,null,32,0,NO,nextval('user_discogs_collection_id_seq'::regclass),1
public,user_discogs_collection,userId,text,null,null,null,NO,null,2
public,user_discogs_collection,artistName,text,null,null,null,NO,null,3
public,user_discogs_collection,albumTitle,text,null,null,null,NO,null,4
public,user_discogs_collection,discogsReleaseId,bigint,null,64,0,NO,null,5
public,user_discogs_collection,year,integer,null,32,0,YES,null,6
public,user_discogs_collection,format,text,null,null,null,YES,null,7
public,user_discogs_collection,albumCoverUrl,text,null,null,null,YES,null,8
public,user_discogs_collection,syncedAt,timestamp without time zone,null,null,null,YES,now(),9
public,user_fcm_tokens,id,integer,null,32,0,NO,nextval('user_fcm_tokens_id_seq'::regclass),1
public,user_fcm_tokens,userId,text,null,null,null,NO,null,2
public,user_fcm_tokens,token,text,null,null,null,NO,null,3
public,user_fcm_tokens,createdAt,timestamp without time zone,null,null,null,YES,now(),4
public,user_fcm_tokens,lastUsed,timestamp without time zone,null,null,null,YES,now(),5
public,users,id,text,null,null,null,NO,null,1
public,users,name,text,null,null,null,YES,null,2
public,users,email,text,null,null,null,YES,null,3
public,users,emailVerified,timestamp without time zone,null,null,null,YES,null,4
public,users,image,text,null,null,null,YES,null,5
public,users,preferredLanguage,text,null,null,null,NO,'fr'::text,6
public,users,emailFrequency,text,null,null,null,NO,'weekly'::text,7
public,users,pushFrequency,text,null,null,null,NO,'weekly'::text,8
public,users,firstRecommendationEmailSent,boolean,null,null,null,NO,false,9
public,users,publicListEnabled,boolean,null,null,null,NO,false,10
public,users,publicListId,text,null,null,null,YES,gen_random_uuid(),11
public,users,publicProfileEnabled,boolean,null,null,null,NO,true,12
public,users,publicRecommendationsEnabled,boolean,null,null,null,NO,true,13
public,users,publicWishlistEnabled,boolean,null,null,null,NO,false,14
public,users,publicCollectionEnabled,boolean,null,null,null,NO,false,15
public,users,last_email_bounce,timestamp without time zone,null,null,null,YES,null,16
public,users,email_bounce_reason,text,null,null,null,YES,null,17
public,users,last_email_complaint,timestamp without time zone,null,null,null,YES,null,18
public,users,email_complaint_reason,text,null,null,null,YES,null,19
public,users,last_email_delivered,timestamp without time zone,null,null,null,YES,null,20
public,users,last_email_opened,timestamp without time zone,null,null,null,YES,null,21
public,users,last_email_clicked,timestamp without time zone,null,null,null,YES,null,22
public,users,email_deliverability_score,integer,null,32,0,YES,100,23
public,users,email_notifications_enabled,boolean,null,null,null,YES,true,24
public,users,last_email_sent,timestamp without time zone,null,null,null,YES,null,25
public,users,timezone,text,null,null,null,YES,'Europe/Paris'::text,26
public,users,createdAt,timestamp without time zone,null,null,null,YES,now(),27
public,users,updatedAt,timestamp without time zone,null,null,null,YES,now(),28
public,users,profile_visibility,USER-DEFINED,null,null,null,NO,'users_only'::profile_visibility,29
public,users,share_recommendations,boolean,null,null,null,NO,true,30
public,users,share_wishlist,boolean,null,null,null,NO,true,31
public,users,share_collection,boolean,null,null,null,NO,true,32
public,users,email_on_new_follower,boolean,null,null,null,NO,true,33
public,verification_tokens,identifier,text,null,null,null,NO,null,1
public,verification_tokens,token,text,null,null,null,NO,null,2
public,verification_tokens,expires,timestamp without time zone,null,null,null,NO,null,3
public,wishlist_items,id,integer,null,32,0,NO,nextval('wishlist_items_id_seq'::regclass),1
public,wishlist_items,userId,text,null,null,null,NO,null,2
public,wishlist_items,artistName,text,null,null,null,NO,null,3
public,wishlist_items,albumTitle,text,null,null,null,NO,null,4
public,wishlist_items,createdAt,timestamp without time zone,null,null,null,YES,now(),5
public,wishlist_items,album_cover_url,text,null,null,null,YES,null,6
public,wishlist_items,spotify_album_id,text,null,null,null,YES,null,7
public,wishlist_items,discogs_release_id,bigint,null,64,0,YES,null,8
public,wishlist_items,affiliate_links,jsonb,null,null,null,YES,null,9
public,wishlist_items,top_track_name,text,null,null,null,YES,null,10
public,wishlist_items,top_track_id,text,null,null,null,YES,null,11
public,wishlist_items,top_track_preview_url,text,null,null,null,YES,null,12
public,wishlist_items,top_track_listen_score,integer,null,32,0,YES,null,13
public,wishlist_items,original_user_name,text,null,null,null,YES,null,14
realtime,messages,topic,text,null,null,null,NO,null,3
realtime,messages,extension,text,null,null,null,NO,null,4
realtime,messages,payload,jsonb,null,null,null,YES,null,5
realtime,messages,event,text,null,null,null,YES,null,6
realtime,messages,private,boolean,null,null,null,YES,false,7
realtime,messages,updated_at,timestamp without time zone,null,null,null,NO,now(),8
realtime,messages,inserted_at,timestamp without time zone,null,null,null,NO,now(),9
realtime,messages,id,uuid,null,null,null,NO,gen_random_uuid(),10
realtime,schema_migrations,version,bigint,null,64,0,NO,null,1
realtime,schema_migrations,inserted_at,timestamp without time zone,null,null,null,YES,null,2
realtime,subscription,id,bigint,null,64,0,NO,null,1
realtime,subscription,subscription_id,uuid,null,null,null,NO,null,2
realtime,subscription,entity,regclass,null,null,null,NO,null,4
realtime,subscription,filters,ARRAY,null,null,null,NO,'{}'::realtime.user_defined_filter[],5
realtime,subscription,claims,jsonb,null,null,null,NO,null,7
realtime,subscription,claims_role,regrole,null,null,null,NO,null,8
realtime,subscription,created_at,timestamp without time zone,null,null,null,NO,"timezone('utc'::text, now())",9
storage,buckets,id,text,null,null,null,NO,null,1
storage,buckets,name,text,null,null,null,NO,null,2
storage,buckets,owner,uuid,null,null,null,YES,null,3
storage,buckets,created_at,timestamp with time zone,null,null,null,YES,now(),4
storage,buckets,updated_at,timestamp with time zone,null,null,null,YES,now(),5
storage,buckets,public,boolean,null,null,null,YES,false,6
storage,buckets,avif_autodetection,boolean,null,null,null,YES,false,7
storage,buckets,file_size_limit,bigint,null,64,0,YES,null,8
storage,buckets,allowed_mime_types,ARRAY,null,null,null,YES,null,9
storage,buckets,owner_id,text,null,null,null,YES,null,10
storage,migrations,id,integer,null,32,0,NO,null,1
storage,migrations,name,character varying,100,null,null,NO,null,2
storage,migrations,hash,character varying,40,null,null,NO,null,3
storage,migrations,executed_at,timestamp without time zone,null,null,null,YES,CURRENT_TIMESTAMP,4
storage,objects,id,uuid,null,null,null,NO,gen_random_uuid(),1
storage,objects,bucket_id,text,null,null,null,YES,null,2
storage,objects,name,text,null,null,null,YES,null,3
storage,objects,owner,uuid,null,null,null,YES,null,4
storage,objects,created_at,timestamp with time zone,null,null,null,YES,now(),5
storage,objects,updated_at,timestamp with time zone,null,null,null,YES,now(),6
storage,objects,last_accessed_at,timestamp with time zone,null,null,null,YES,now(),7
storage,objects,metadata,jsonb,null,null,null,YES,null,8
storage,objects,path_tokens,ARRAY,null,null,null,YES,null,9
storage,objects,version,text,null,null,null,YES,null,10
storage,objects,owner_id,text,null,null,null,YES,null,11
storage,objects,user_metadata,jsonb,null,null,null,YES,null,12
storage,s3_multipart_uploads,id,text,null,null,null,NO,null,1
storage,s3_multipart_uploads,in_progress_size,bigint,null,64,0,NO,0,2
storage,s3_multipart_uploads,upload_signature,text,null,null,null,NO,null,3
storage,s3_multipart_uploads,bucket_id,text,null,null,null,NO,null,4
storage,s3_multipart_uploads,key,text,null,null,null,NO,null,5
storage,s3_multipart_uploads,version,text,null,null,null,NO,null,6
storage,s3_multipart_uploads,owner_id,text,null,null,null,YES,null,7
storage,s3_multipart_uploads,created_at,timestamp with time zone,null,null,null,NO,now(),8
storage,s3_multipart_uploads,user_metadata,jsonb,null,null,null,YES,null,9
storage,s3_multipart_uploads_parts,id,uuid,null,null,null,NO,gen_random_uuid(),1
storage,s3_multipart_uploads_parts,upload_id,text,null,null,null,NO,null,2
storage,s3_multipart_uploads_parts,size,bigint,null,64,0,NO,0,3
storage,s3_multipart_uploads_parts,part_number,integer,null,32,0,NO,null,4
storage,s3_multipart_uploads_parts,bucket_id,text,null,null,null,NO,null,5
storage,s3_multipart_uploads_parts,key,text,null,null,null,NO,null,6
storage,s3_multipart_uploads_parts,etag,text,null,null,null,NO,null,7
storage,s3_multipart_uploads_parts,owner_id,text,null,null,null,YES,null,8
storage,s3_multipart_uploads_parts,version,text,null,null,null,NO,null,9
storage,s3_multipart_uploads_parts,created_at,timestamp with time zone,null,null,null,NO,now(),10
vault,secrets,id,uuid,null,null,null,NO,gen_random_uuid(),1
vault,secrets,name,text,null,null,null,YES,null,2
vault,secrets,description,text,null,null,null,NO,''::text,3
vault,secrets,secret,text,null,null,null,NO,null,4
vault,secrets,key_id,uuid,null,null,null,YES,null,5
vault,secrets,nonce,bytea,null,null,null,YES,vault._crypto_aead_det_noncegen(),6
vault,secrets,created_at,timestamp with time zone,null,null,null,NO,CURRENT_TIMESTAMP,7
vault,secrets,updated_at,timestamp with time zone,null,null,null,NO,CURRENT_TIMESTAMP,8