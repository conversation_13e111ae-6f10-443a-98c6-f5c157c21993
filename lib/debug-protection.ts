import { NextResponse } from 'next/server';

/**
 * V<PERSON>rifie si l'environnement actuel est 'development'.
 * Si ce n'est pas le cas, retourne une réponse 404 pour bloquer l'accès.
 * @returns {NextResponse | null} Une réponse 404 si l'accès est interdit, sinon null.
 */
export function protectDebugRoute() {
  if (process.env.NODE_ENV !== 'development') {
    return new NextResponse(null, { status: 404 });
  }
  return null;
}