[{"TimeUTC": "2025-07-21 10:50:13", "timestampInMs": 1753095013095, "requestPath": "app.stream2spin.com/u/13c7ff29-c572-48a6-bcd6-c184b2a082b4", "requestMethod": "GET", "requestQueryString": "", "responseStatusCode": -1, "requestId": "bgwhv-1753095011532-eda870bf947f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "error", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/u/[publicListId]", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Erreur lors de l'enregistrement analytics: Error: Failed query: select \"id\", \"userId\", \"publicListId\", \"eventType\", \"eventData\", \"ipAddress\", \"userAgent\", \"referrer\", \"timestamp\" from \"public_list_analytics\" \"publicListAnalytics\" where (\"publicListAnalytics\".\"publicListId\" = $1 and \"publicListAnalytics\".\"eventType\" = $2 and \"publicListAnalytics\".\"ipAddress\" = $3 and \"publicListAnalytics\".\"timestamp\" > NOW() - INTERVAL '5 minutes') limit $4\nparams: 13c7ff29-c572-48a6-bcd6-c184b2a082b4,view,************,1\n    at eS.queryWithCache (.next/server/chunks/8420.js:8:682)\n    at async (.next/server/chunks/8420.js:8:3175)\n    at async d (.next/server/app/u/[publicListId]/page.js:1:66261)\n    at async b (.next/server/app/u/[publicListId]/page.js:1:52899) {\n  query: `select \"id\", \"userId\", \"publicListId\", \"eventType\", \"eventData\", \"ipAddress\", \"userAgent\", \"referrer\", \"timestamp\" from \"public_list_analytics\" \"publicListAnalytics\" where (\"publicListAnalytics\".\"publicListId\" = $1 and \"publicListAnalytics\".\"eventType\" = $2 and \"publicListAnalytics\".\"ipAddress\" = $3 and \"publicListAnalytics\".\"timestamp\" > NOW() - INTERVAL '5 minutes') limit $4`,\n  params: [Array],\n  [cause]: c: relation \"public_list_analytics\" does not exist\n      at X (.next/server/chunks/8420.js:11:2034)\n      at <unknown> (.next/server/chunks/8420.js:11:3080)\n      at TLSSocket.ez (.next/server/chunks/8420.js:11:3084) {\n    severity_local: 'ERROR',\n    severity: 'ERROR',\n    code: '42P01',\n    position: '121',\n    file: 'parse_relation.c',\n    line: '1449',\n    routine: 'parserOpenTable'\n  }\n}", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P9NZ8WVSEXCP9R5SQDZ2XX", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:50:11", "timestampInMs": 1753095011815, "requestPath": "app.stream2spin.com/u/13c7ff29-c572-48a6-bcd6-c184b2a082b4", "requestMethod": "GET", "requestQueryString": "", "responseStatusCode": -1, "requestId": "bgwhv-1753095011532-eda870bf947f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/u/[publicListId]", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "🚀 Cache DB hit en 0ms pour: ad00d67c-40a1-4219-82ec-16c78b1d7be9", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P9NZ8WVSEXCP9R5SQDZ2XX", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:50:11", "timestampInMs": 1753095011806, "requestPath": "app.stream2spin.com/u/13c7ff29-c572-48a6-bcd6-c184b2a082b4", "requestMethod": "GET", "requestQueryString": "", "responseStatusCode": -1, "requestId": "bgwhv-1753095011532-eda870bf947f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/u/[publicListId]", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "🚀 Cache DB hit en 0ms pour: ad00d67c-40a1-4219-82ec-16c78b1d7be9", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P9NZ8WVSEXCP9R5SQDZ2XX", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:50:11", "timestampInMs": 1753095011635, "requestPath": "app.stream2spin.com/u/13c7ff29-c572-48a6-bcd6-c184b2a082b4", "requestMethod": "GET", "requestQueryString": "", "responseStatusCode": -1, "requestId": "bgwhv-1753095011532-eda870bf947f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/u/[publicListId]", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "🚀 Cache DB hit en 0ms pour: ad00d67c-40a1-4219-82ec-16c78b1d7be9", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P9NZ8WVSEXCP9R5SQDZ2XX", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:50:11", "timestampInMs": 1753095011635, "requestPath": "app.stream2spin.com/u/13c7ff29-c572-48a6-bcd6-c184b2a082b4", "requestMethod": "GET", "requestQueryString": "", "responseStatusCode": -1, "requestId": "bgwhv-1753095011532-eda870bf947f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/u/[publicListId]", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "🚀 Cache DB hit en 0ms pour: ad00d67c-40a1-4219-82ec-16c78b1d7be9", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P9NZ8WVSEXCP9R5SQDZ2XX", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:50:11", "timestampInMs": 1753095011612, "requestPath": "app.stream2spin.com/u/13c7ff29-c572-48a6-bcd6-c184b2a082b4", "requestMethod": "GET", "requestQueryString": "", "responseStatusCode": 200, "requestId": "bgwhv-1753095011532-eda870bf947f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/u/[publicListId]", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": 1483, "region": "iad1", "maxMemoryUsed": 192, "memorySize": 2048, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P9NZ8WVSEXCP9R5SQDZ2XX", "instanceId": "29rxhVnKQG44"}, {"TimeUTC": "2025-07-21 10:50:11", "timestampInMs": 1753095011542, "requestPath": "app.stream2spin.com/u/13c7ff29-c572-48a6-bcd6-c184b2a082b4", "requestMethod": "GET", "requestQueryString": "", "responseStatusCode": 200, "requestId": "bgwhv-1753095011532-eda870bf947f", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "edge-middleware", "function": "/middleware", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "lhr1", "maxMemoryUsed": -1, "memorySize": -1, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P9NZ6PJTDJFX5QMS8ZPWP1", "instanceId": "Xh1pYGPpfHrG"}, {"TimeUTC": "2025-07-21 10:40:46", "timestampInMs": 1753094446128, "requestPath": "app.stream2spin.com/u/4233fcfa-0a88-4fd2-88d9-4d8891d080e2", "requestMethod": "GET", "requestQueryString": "", "responseStatusCode": -1, "requestId": "ntwfq-1753094446011-890eb36e82f2", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "error", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/u/[publicListId]", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Error queueing profile refreshes: Error [QstashDailyRatelimitError]: Exceeded daily rate limit. {\"limit\":\"500\",\"remaining\":\"0\",\"reset\":\"1753142400\"}\n    at C.checkResponse (.next/server/chunks/8644.js:1:31462)\n    at C.requestWithBackoff (.next/server/chunks/8644.js:1:30547)\n    at async C.request (.next/server/chunks/8644.js:1:29763)\n    at async Q.publish (.next/server/chunks/8644.js:1:45429)\n    at async Q.publishJSON (.next/server/chunks/8644.js:1:45685)\n    at async i (.next/server/chunks/5355.js:1:463)\n    at async R (.next/server/chunks/5355.js:1:10956) {\n  status: 429,\n  limit: '500',\n  remaining: '0',\n  reset: '1753142400'\n}", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P94Q0RETPZ860XXQC1YWWX", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:40:46", "timestampInMs": 1753094446103, "requestPath": "app.stream2spin.com/u/4233fcfa-0a88-4fd2-88d9-4d8891d080e2", "requestMethod": "GET", "requestQueryString": "", "responseStatusCode": 200, "requestId": "ntwfq-1753094446011-890eb36e82f2", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/u/[publicListId]", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": 20, "region": "iad1", "maxMemoryUsed": 155, "memorySize": 2048, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P94Q0RETPZ860XXQC1YWWX", "instanceId": "uD5SMCHkfAoe"}, {"TimeUTC": "2025-07-21 10:40:46", "timestampInMs": 1753094446024, "requestPath": "app.stream2spin.com/u/4233fcfa-0a88-4fd2-88d9-4d8891d080e2", "requestMethod": "GET", "requestQueryString": "", "responseStatusCode": 200, "requestId": "ntwfq-1753094446011-890eb36e82f2", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "edge-middleware", "function": "/middleware", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "lhr1", "maxMemoryUsed": -1, "memorySize": -1, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P94PY8P0M2TDHMC662GG5V", "instanceId": "Xh1pYGPpfHrG"}, {"TimeUTC": "2025-07-21 10:37:09", "timestampInMs": 1753094229452, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "5ckk4-1753094228552-7abe7d164598", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "error", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "❌ Erreur lors de la génération interactive: Error: Aucune recommandation générée\n    at <unknown> (.next/server/app/api/generation-stream/route.js:3:2472)", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8Y2KWXYMDEZM2REMS8A8H", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:37:09", "timestampInMs": 1753094229441, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "5ckk4-1753094228552-7abe7d164598", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "📊 Total recommandations: 0", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8Y2KWXYMDEZM2REMS8A8H", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:37:08", "timestampInMs": 1753094228634, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "5ckk4-1753094228552-7abe7d164598", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "🚀 Cache DB hit en 0ms pour: 98fe2c93-84ff-4a2c-b1a0-dab1c38881b2", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8Y2KWXYMDEZM2REMS8A8H", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:37:08", "timestampInMs": 1753094228604, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": 200, "requestId": "5ckk4-1753094228552-7abe7d164598", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": 939, "region": "iad1", "maxMemoryUsed": 150, "memorySize": 2048, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8Y2KWXYMDEZM2REMS8A8H", "instanceId": "YlthU8p31Er1"}, {"TimeUTC": "2025-07-21 10:36:43", "timestampInMs": 1753094203404, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "l689h-1753094202591-f243073dac28", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "error", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "❌ Erreur lors de la génération interactive: Error: Aucune recommandation générée\n    at <unknown> (.next/server/app/api/generation-stream/route.js:3:2472)", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8X98G0XATSTSFJPCTZD50", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:36:43", "timestampInMs": 1753094203403, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "l689h-1753094202591-f243073dac28", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "📊 Total recommandations: 0", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8X98G0XATSTSFJPCTZD50", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:36:42", "timestampInMs": 1753094202659, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "l689h-1753094202591-f243073dac28", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "🚀 Cache DB hit en 0ms pour: 98fe2c93-84ff-4a2c-b1a0-dab1c38881b2", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8X98G0XATSTSFJPCTZD50", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:36:42", "timestampInMs": 1753094202640, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": 200, "requestId": "l689h-1753094202591-f243073dac28", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": 853, "region": "iad1", "maxMemoryUsed": 156, "memorySize": 2048, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8X98G0XATSTSFJPCTZD50", "instanceId": "f67OZgCalbfT"}, {"TimeUTC": "2025-07-21 10:36:40", "timestampInMs": 1753094200670, "requestPath": "app.stream2spin.com/generating", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "hvfpl-1753094200300-1b95a8bbaa14", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "error", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/generating", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "❌ Erreur lors de la revalidation et redirection: Error: NEXT_REDIRECT\n    at i (.next/server/app/generating/page.js:1:3755)\n    at o (.next/server/app/generating/page.js:1:4048)\n    at i (.next/server/app/generating/page.js:14:9129) {\n  digest: 'NEXT_REDIRECT;push;/recommendations?from=generating;307;'\n}", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8X73WB3VSW603NDVHWVM0", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:36:40", "timestampInMs": 1753094200444, "requestPath": "app.stream2spin.com/generating", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 303, "requestId": "hvfpl-1753094200300-1b95a8bbaa14", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/generating", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": 2986, "region": "iad1", "maxMemoryUsed": 120, "memorySize": 2048, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8X73WB3VSW603NDVHWVM0", "instanceId": "EynaDg7P3DSf"}, {"TimeUTC": "2025-07-21 10:36:40", "timestampInMs": 1753094200353, "requestPath": "app.stream2spin.com/generating", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "hvfpl-1753094200300-1b95a8bbaa14", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "edge-middleware", "function": "/middleware", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "lhr1", "maxMemoryUsed": -1, "memorySize": -1, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8X712HNWKHEXCAW4RZS07", "instanceId": "Ne4uyQx2Ew6E"}, {"TimeUTC": "2025-07-21 10:36:39", "timestampInMs": 1753094199370, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "8vz5v-1753094198552-963fb06e68ff", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "error", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "❌ Erreur lors de la génération interactive: Error: Aucune recommandation générée\n    at <unknown> (.next/server/app/api/generation-stream/route.js:3:2472)", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8X5A93VBK9B8DK5BWG05F", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:36:39", "timestampInMs": 1753094199368, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "8vz5v-1753094198552-963fb06e68ff", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "📊 Total recommandations: 0", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8X5A93VBK9B8DK5BWG05F", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:36:38", "timestampInMs": 1753094198621, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "8vz5v-1753094198552-963fb06e68ff", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "🚀 Cache DB hit en 0ms pour: 98fe2c93-84ff-4a2c-b1a0-dab1c38881b2", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8X5A93VBK9B8DK5BWG05F", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:36:38", "timestampInMs": 1753094198601, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": 200, "requestId": "8vz5v-1753094198552-963fb06e68ff", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": 858, "region": "iad1", "maxMemoryUsed": 156, "memorySize": 2048, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8X5A93VBK9B8DK5BWG05F", "instanceId": "f67OZgCalbfT"}, {"TimeUTC": "2025-07-21 10:36:35", "timestampInMs": 1753094195343, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "m4bf6-1753094194506-0dbbfd494280", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "error", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "❌ Erreur lors de la génération interactive: Error: Aucune recommandation générée\n    at <unknown> (.next/server/app/api/generation-stream/route.js:3:2472)", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8X1BWW8EB25D28CBFWH2G", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:36:35", "timestampInMs": 1753094195341, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "m4bf6-1753094194506-0dbbfd494280", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "📊 Total recommandations: 0", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8X1BWW8EB25D28CBFWH2G", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:36:34", "timestampInMs": 1753094194575, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "m4bf6-1753094194506-0dbbfd494280", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "🚀 Cache DB hit en 0ms pour: 98fe2c93-84ff-4a2c-b1a0-dab1c38881b2", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8X1BWW8EB25D28CBFWH2G", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:36:34", "timestampInMs": 1753094194556, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": 200, "requestId": "m4bf6-1753094194506-0dbbfd494280", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": 876, "region": "iad1", "maxMemoryUsed": 156, "memorySize": 2048, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8X1BWW8EB25D28CBFWH2G", "instanceId": "f67OZgCalbfT"}, {"TimeUTC": "2025-07-21 10:36:32", "timestampInMs": 1753094192259, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "58dqm-1753094190962-eeae0c771977", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "error", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "❌ Erreur lors de la génération interactive: Error: Aucune recommandation générée\n    at <unknown> (.next/server/app/api/generation-stream/route.js:3:2472)", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8WXVT1FDFSKW991Y9C71A", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:36:32", "timestampInMs": 1753094192248, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "58dqm-1753094190962-eeae0c771977", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "📊 Total recommandations: 0", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8WXVT1FDFSKW991Y9C71A", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:36:31", "timestampInMs": 1753094191441, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "58dqm-1753094190962-eeae0c771977", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "✅ Données utilisateur mises en cache DB (30min): 98fe2c93-84ff-4a2c-b1a0-dab1c38881b2", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8WXVT1FDFSKW991Y9C71A", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:36:31", "timestampInMs": 1753094191441, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "58dqm-1753094190962-eeae0c771977", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "⚡ Requête DB utilisateur en 178ms pour: 98fe2c93-84ff-4a2c-b1a0-dab1c38881b2", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8WXVT1FDFSKW991Y9C71A", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:36:31", "timestampInMs": 1753094191263, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": -1, "requestId": "58dqm-1753094190962-eeae0c771977", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "💾 <PERSON><PERSON> miss pour l'utilisateur: 98fe2c93-84ff-4a2c-b1a0-dab1c38881b2", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8WXVT1FDFSKW991Y9C71A", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:36:31", "timestampInMs": 1753094191250, "requestPath": "app.stream2spin.com/api/generation-stream", "requestMethod": "GET", "requestQueryString": "userId=98fe2c93-84ff-4a2c-b1a0-dab1c38881b2&lang=fr", "responseStatusCode": 200, "requestId": "58dqm-1753094190962-eeae0c771977", "requestUserAgent": "Mozilla/5.0 (Linux; Android 11; AC2003 Build/RP1A.201005.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.45 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/515.**********;]", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/api/generation-stream", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": 1108, "region": "iad1", "maxMemoryUsed": 116, "memorySize": 2048, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8WXVT1FDFSKW991Y9C71A", "instanceId": "f67OZgCalbfT"}, {"TimeUTC": "2025-07-21 10:31:27", "timestampInMs": 1753093887911, "requestPath": "app.stream2spin.com/u/4233fcfa-0a88-4fd2-88d9-4d8891d080e2", "requestMethod": "GET", "requestQueryString": "", "responseStatusCode": -1, "requestId": "bwssp-1753093887819-6b6fde06b244", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "error", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/u/[publicListId]", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Error queueing profile refreshes: Error [QstashDailyRatelimitError]: Exceeded daily rate limit. {\"limit\":\"500\",\"remaining\":\"0\",\"reset\":\"1753142400\"}\n    at C.checkResponse (.next/server/chunks/8644.js:1:31462)\n    at C.requestWithBackoff (.next/server/chunks/8644.js:1:30547)\n    at async C.request (.next/server/chunks/8644.js:1:29763)\n    at async Q.publish (.next/server/chunks/8644.js:1:45429)\n    at async Q.publishJSON (.next/server/chunks/8644.js:1:45685)\n    at async i (.next/server/chunks/5355.js:1:463)\n    at async R (.next/server/chunks/5355.js:1:10956) {\n  status: 429,\n  limit: '500',\n  remaining: '0',\n  reset: '1753142400'\n}", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8KNWTVJQ3B3M39GX0JCMW", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:31:27", "timestampInMs": 1753093887898, "requestPath": "app.stream2spin.com/u/4233fcfa-0a88-4fd2-88d9-4d8891d080e2", "requestMethod": "GET", "requestQueryString": "", "responseStatusCode": 200, "requestId": "bwssp-1753093887819-6b6fde06b244", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/u/[publicListId]", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": 9, "region": "iad1", "maxMemoryUsed": 242, "memorySize": 2048, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8KNWTVJQ3B3M39GX0JCMW", "instanceId": "N4TPstd3DACB"}, {"TimeUTC": "2025-07-21 10:31:27", "timestampInMs": 1753093887830, "requestPath": "app.stream2spin.com/u/4233fcfa-0a88-4fd2-88d9-4d8891d080e2", "requestMethod": "GET", "requestQueryString": "", "responseStatusCode": 200, "requestId": "bwssp-1753093887819-6b6fde06b244", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "edge-middleware", "function": "/middleware", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "lhr1", "maxMemoryUsed": -1, "memorySize": -1, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8KNTP7NEGNWPVWTX809G0", "instanceId": "D5njbHxRl4rx"}, {"TimeUTC": "2025-07-21 10:30:03", "timestampInMs": 1753093803490, "requestPath": "app.stream2spin.com/social", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "wmjc5-1753093803231-0c0f14da5aaf", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "error", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/social", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Error queueing profile refreshes: Error [QstashDailyRatelimitError]: Exceeded daily rate limit. {\"limit\":\"500\",\"remaining\":\"0\",\"reset\":\"1753142400\"}\n    at C.checkResponse (.next/server/chunks/8644.js:1:31462)\n    at C.requestWithBackoff (.next/server/chunks/8644.js:1:30547)\n    at async C.request (.next/server/chunks/8644.js:1:29763)\n    at async Q.publish (.next/server/chunks/8644.js:1:45429)\n    at async Q.publishJSON (.next/server/chunks/8644.js:1:45685)\n    at async i (.next/server/chunks/5355.js:1:463)\n    at async R (.next/server/chunks/5355.js:1:10956) {\n  status: 429,\n  limit: '500',\n  remaining: '0',\n  reset: '1753142400'\n}", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8H39FENSXTZN0YGWVZD9F", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:30:03", "timestampInMs": 1753093803327, "requestPath": "app.stream2spin.com/social", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "wmjc5-1753093803231-0c0f14da5aaf", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/social", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "🚀 Cache DB hit en 0ms pour: ad00d67c-40a1-4219-82ec-16c78b1d7be9", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8H39FENSXTZN0YGWVZD9F", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:30:03", "timestampInMs": 1753093803311, "requestPath": "app.stream2spin.com/social", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "wmjc5-1753093803231-0c0f14da5aaf", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/social", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": 180, "region": "iad1", "maxMemoryUsed": 242, "memorySize": 2048, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8H39FENSXTZN0YGWVZD9F", "instanceId": "N4TPstd3DACB"}, {"TimeUTC": "2025-07-21 10:30:03", "timestampInMs": 1753093803242, "requestPath": "app.stream2spin.com/social", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "wmjc5-1753093803231-0c0f14da5aaf", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "edge-middleware", "function": "/middleware", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "lhr1", "maxMemoryUsed": -1, "memorySize": -1, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8H37ABV59E6XR233GG7P2", "instanceId": "D5njbHxRl4rx"}, {"TimeUTC": "2025-07-21 10:29:48", "timestampInMs": 1753093788892, "requestPath": "app.stream2spin.com/social", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m4bf6-1753093788599-65bee783e6ce", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "error", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/social", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "Error queueing profile refreshes: Error [QstashDailyRatelimitError]: Exceeded daily rate limit. {\"limit\":\"500\",\"remaining\":\"0\",\"reset\":\"1753142400\"}\n    at C.checkResponse (.next/server/chunks/8644.js:1:31462)\n    at C.requestWithBackoff (.next/server/chunks/8644.js:1:30547)\n    at async C.request (.next/server/chunks/8644.js:1:29763)\n    at async Q.publish (.next/server/chunks/8644.js:1:45429)\n    at async Q.publishJSON (.next/server/chunks/8644.js:1:45685)\n    at async i (.next/server/chunks/5355.js:1:463)\n    at async R (.next/server/chunks/5355.js:1:10956) {\n  status: 429,\n  limit: '500',\n  remaining: '0',\n  reset: '1753142400'\n}", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8GN0KHK9THAXNQ7ZCXYRQ", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:29:48", "timestampInMs": 1753093788720, "requestPath": "app.stream2spin.com/social", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": -1, "requestId": "m4bf6-1753093788599-65bee783e6ce", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/social", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "iad1", "maxMemoryUsed": -1, "memorySize": -1, "message": "🚀 Cache DB hit en 0ms pour: ad00d67c-40a1-4219-82ec-16c78b1d7be9", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8GN0KHK9THAXNQ7ZCXYRQ", "instanceId": ""}, {"TimeUTC": "2025-07-21 10:29:48", "timestampInMs": 1753093788691, "requestPath": "app.stream2spin.com/social", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "m4bf6-1753093788599-65bee783e6ce", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "lambda", "function": "/social", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": 192, "region": "iad1", "maxMemoryUsed": 142, "memorySize": 2048, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8GN0KHK9THAXNQ7ZCXYRQ", "instanceId": "N4TPstd3DACB"}, {"TimeUTC": "2025-07-21 10:29:48", "timestampInMs": 1753093788615, "requestPath": "app.stream2spin.com/social", "requestMethod": "POST", "requestQueryString": "", "responseStatusCode": 200, "requestId": "m4bf6-1753093788599-65bee783e6ce", "requestUserAgent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "level": "info", "environment": "production", "branch": "main", "vercelCache": "MISS", "type": "edge-middleware", "function": "/middleware", "host": "app.stream2spin.com", "deploymentDomain": "app.stream2spin.com", "deploymentId": "dpl_848Maue6LP7KkR54q95eszNiLFrC", "durationMs": -1, "region": "lhr1", "maxMemoryUsed": -1, "memorySize": -1, "message": "", "projectId": "prj_rTXVOrYAFMQrwVjT1I3cQvI6tAKu", "traceId": "", "sessionId": "", "invocationId": "01K0P8GMY7TRM6CAYC7WF787YR", "instanceId": "Xh1pYGPpfHrG"}]