import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/components/providers/session-provider";
import { AuthenticatedLayout } from "@/components/layout/authenticated-layout";
import { GlobalAudioPlayer } from "@/components/audio/global-audio-player";
import { ClientToaster } from "@/components/ui/client-toaster";
import { Toaster as SonnerToaster } from "sonner";
import { AuthModal } from "@/components/public/auth-modal";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';

import { SplashScreenProvider } from "@/components/providers/splash-screen-provider";
import { LoadingBarProvider } from "@/components/providers/loading-bar-provider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  manifest: "/manifest.json",
  title: "Stream2Spin",
  description: "Transformez vos habitudes d'écoute Spotify en recommandations de vinyles personnalisées",
  icons: {
    icon: [
      {
        url: '/Stream2Spin_icon.svg',
        type: 'image/svg+xml',
      },
    ],
    shortcut: '/Stream2Spin_icon.svg',
    apple: '/Stream2Spin_icon.svg',
  },
  robots: {
    index: false,
    follow: false,
    nocache: true,
    googleBot: {
      index: false,
      follow: false,
      noimageindex: true,
      'max-video-preview': -1,
      'max-image-preview': 'none',
      'max-snippet': -1,
    },
  },
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Providing all messages to the client side
  const messages = await getMessages();

  return (
    <html lang="fr">
      <head>
        <meta name="theme-color" content="#111827" />
        <meta name="robots" content="noindex, nofollow, nocache, noarchive, nosnippet, noimageindex" />
        <meta name="googlebot" content="noindex, nofollow, nocache, noarchive, nosnippet, noimageindex" />
        <meta name="bingbot" content="noindex, nofollow, nocache, noarchive, nosnippet, noimageindex" />
      </head>
      <body className={inter.className} suppressHydrationWarning={true}>
        <NextIntlClientProvider messages={messages}>
          <AuthProvider>
            <LoadingBarProvider>
              <SplashScreenProvider>
                <AuthenticatedLayout>
                  {children}
                </AuthenticatedLayout>
                {/* US 3.6: Lecteur audio global pour les extraits de titres phares */}
                <GlobalAudioPlayer />
                {/* Modale d'authentification globale */}
                <AuthModal />
              </SplashScreenProvider>
            </LoadingBarProvider>
          </AuthProvider>
        </NextIntlClientProvider>
        <ClientToaster />
        <SonnerToaster
          position="top-right"
          richColors
          closeButton
          duration={4000}
        />
      </body>
    </html>
  );
}
