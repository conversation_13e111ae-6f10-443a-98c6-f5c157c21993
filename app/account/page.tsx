import { getSession } from "@/lib/auth";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import { users, accounts } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ProfileCard } from "@/components/account/profile-card";
import { ConnectedAccountsCard } from "@/components/account/connected-accounts-card";
import { ProfileVisibilityCard } from "@/components/account/profile-visibility-card";
import { NotificationSettingsCard } from "@/components/account/notification-settings-card";
import { LanguageSettingsCard } from "@/components/account/language-settings-card";
import { DangerZoneCard } from "@/components/account/danger-zone-card";
import { SuccessToastHandler } from "@/components/account/success-toast-handler";
import { getTranslations } from 'next-intl/server';
import { getProfileVisibilitySettings } from "@/app/actions/profile";

export default async function AccountPage() {
  try {
    const session = await getSession();
    const t = await getTranslations('account');
    const profileT = await getTranslations('account.profile');

    // Vérifier si l'utilisateur est connecté
    if (!session?.user?.id) {
      redirect("/login?callbackUrl=/account");
    }

    // Récupérer les données utilisateur depuis la base de données
    const userRecord = await db.query.users.findFirst({
      where: eq(users.id, session.user.id),
      columns: {
        id: true,
        name: true,
        email: true,
        profileVisibility: true,
        shareRecommendations: true,
        shareWishlist: true,
        shareCollection: true,
        publicListEnabled: true,
        publicListId: true,
        emailFrequency: true,
        pushFrequency: true,
        emailOnNewFollower: true,
        preferredLanguage: true,
      },
    });

    // Vérifier le statut de connexion Discogs
    const discogsAccount = await db.query.accounts.findFirst({
      where: and(
        eq(accounts.userId, session.user.id),
        eq(accounts.provider, "discogs")
      ),
    });

    const isDiscogsConnected = !!discogsAccount;

    const visibilitySettings = {
      profileVisibility: userRecord?.profileVisibility || 'users_only',
      shareRecommendations: userRecord?.shareRecommendations ?? true,
      shareWishlist: userRecord?.shareWishlist ?? true,
      shareCollection: userRecord?.shareCollection ?? true,
    };

    return (
      <div className="container py-12">
        <SuccessToastHandler />
        <div className="space-y-8">

          {/* ... (Header) */}

          {/* Section Profil */}
          <ProfileCard
            user={session.user}
            translations={{
              title: profileT('title'),
              description: profileT('description'), 
              username: profileT('username'),
              email: profileT('email'),
              profilePicture: profileT('profilePicture'),
              syncedFromSpotify: profileT('syncedFromSpotify'),
              notDefined: profileT('notDefined'),
            }}
          />

          <Separator />

          {/* Section Comptes Connectés */}
          <ConnectedAccountsCard isDiscogsConnected={isDiscogsConnected} />

          <Separator />

          {/* Section Visibilité du Profil */}
          <ProfileVisibilityCard initialSettings={visibilitySettings} />
          
          <Separator />

          {/* Section Préférences de Notification */}
          <NotificationSettingsCard
            emailFrequency={userRecord?.emailFrequency || "weekly"}
            pushFrequency={userRecord?.pushFrequency || "weekly"}
            emailOnNewFollower={userRecord?.emailOnNewFollower ?? true}
          />

          <Separator />

          {/* Section Gestion de la Langue */}
          <LanguageSettingsCard
            currentLanguage={userRecord?.preferredLanguage || "fr"}
          />

          <Separator />

          {/* Zone de Danger */}
          <DangerZoneCard />

        </div>
      </div>
    );
  } catch (error) {
    console.error("Erreur lors du rendu de la page account:", error);
    return (
      <div className="container py-12">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Erreur</h1>
          <p className="text-muted-foreground mt-2">
            Une erreur s'est produite lors du chargement de la page.
          </p>
        </div>
      </div>
    );
  }
}
