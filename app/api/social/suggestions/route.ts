import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth';
import { db } from '@/lib/db';
import { users, followers } from '@/lib/db/schema';
import { and, eq, ne, count, desc, inArray, notInArray, sql } from 'drizzle-orm';

export interface ProfileSuggestion {
  id: string;
  name: string | null;
  image: string | null;
  publicListId: string | null;
  mutualFollowers: number;
  isDefaultProfile?: boolean;
}

/**
 * API Route pour les suggestions de profils
 * Implémente l'algorithme "Amis d'Amis" avec profil par défaut
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Non authentifié' },
        { status: 401 }
      );
    }

    const currentUserId = session.user.id;
    const suggestions: ProfileSuggestion[] = [];

    // 1. Récupérer l'ID du profil par défaut (<EMAIL>)
    const defaultProfile = await db.query.users.findFirst({
      where: eq(users.email, '<EMAIL>'),
      columns: {
        id: true,
        name: true,
        image: true,
        publicListId: true,
      },
    });

    // 2. Vérifier si l'utilisateur suit déjà le profil par défaut
    let isFollowingDefault = false;
    if (defaultProfile) {
      const followingDefault = await db.query.followers.findFirst({
        where: and(
          eq(followers.followerId, currentUserId),
          eq(followers.followingId, defaultProfile.id)
        ),
      });
      isFollowingDefault = !!followingDefault;
    }

    // 3. Ajouter le profil par défaut en première position si pas encore suivi
    if (defaultProfile && !isFollowingDefault && defaultProfile.id !== currentUserId) {
      suggestions.push({
        id: defaultProfile.id,
        name: defaultProfile.name,
        image: defaultProfile.image,
        publicListId: defaultProfile.publicListId,
        mutualFollowers: 0,
        isDefaultProfile: true,
      });
    }

    // 4. Récupérer les utilisateurs que l'utilisateur actuel suit
    const currentUserFollowing = await db.query.followers.findMany({
      where: eq(followers.followerId, currentUserId),
      columns: { followingId: true },
    });
    const followingIds = currentUserFollowing.map(f => f.followingId);

    // 5. Algorithme "Amis d'Amis" amélioré
    let mutualSuggestions: ProfileSuggestion[] = [];

    if (followingIds.length > 0) {
      // Cas où l'utilisateur suit déjà des personnes
      mutualSuggestions = await db
        .select({
          id: users.id,
          name: users.name,
          image: users.image,
          publicListId: users.publicListId,
          mutualFollowers: count().as('mutualFollowers'),
        })
        .from(users)
        .innerJoin(followers, eq(followers.followingId, users.id))
        .where(
          and(
            ne(users.id, currentUserId),
            ne(users.profileVisibility, 'private'),
            inArray(followers.followerId, followingIds),
            // Exclure les utilisateurs déjà suivis
            notInArray(users.id, followingIds),
            // Exclure le profil par défaut s'il est déjà ajouté
            defaultProfile ? ne(users.id, defaultProfile.id) : sql`true`
          )
        )
        .groupBy(users.id, users.name, users.image, users.publicListId)
        .orderBy(desc(count()))
        .limit(15);
    } else {
      // Cas où l'utilisateur ne suit personne encore : suggestions basées sur l'activité
      mutualSuggestions = await db
        .select({
          id: users.id,
          name: users.name,
          image: users.image,
          publicListId: users.publicListId,
          mutualFollowers: sql<number>`0`.as('mutualFollowers'),
        })
        .from(users)
        .where(
          and(
            ne(users.id, currentUserId),
            ne(users.profileVisibility, 'private'),
            // Exclure le profil par défaut s'il est déjà ajouté
            defaultProfile ? ne(users.id, defaultProfile.id) : sql`true`
          )
        )
        .orderBy(desc(users.createdAt))
        .limit(15);
    }

    // 6. Ajouter les suggestions mutuelles
    suggestions.push(
      ...mutualSuggestions.map(s => ({
        ...s,
        mutualFollowers: Number(s.mutualFollowers),
        isDefaultProfile: false,
      }))
    );

    // 7. Limiter à 10 suggestions maximum
    const finalSuggestions = suggestions.slice(0, 10);

    return NextResponse.json({
      success: true,
      suggestions: finalSuggestions,
    });

  } catch (error) {
    console.error('Erreur API suggestions:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}
