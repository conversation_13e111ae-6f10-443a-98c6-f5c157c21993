import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";
import { protectDebugRoute } from "@/lib/debug-protection";

/**
 * API de diagnostic pour comparer l'état des tables auth entre dev et staging
 * Identifie les différences de structure qui causent les problèmes de login
 */
export async function GET(request: NextRequest) {
  const protection = protectDebugRoute();
  if (protection) return protection;

  try {
    console.log("🔍 Diagnostic des tables auth pour comparaison DEV vs STAGING...");

    const diagnostics = {
      environment: process.env.NODE_ENV || 'unknown',
      vercelEnv: process.env.VERCEL_ENV || 'local',
      connection: false,
      tables: {} as Record<string, any>,
      issues: [] as string[],
      recommendations: [] as string[],
      timestamp: new Date().toISOString()
    };

    // 1. Test de connexion
    try {
      await db.execute(sql`SELECT 1 as test`);
      diagnostics.connection = true;
      console.log("✅ Connexion DB réussie");
    } catch (error) {
      diagnostics.issues.push(`Erreur connexion DB: ${error}`);
      console.error("❌ Erreur connexion DB:", error);
    }

    // 2. Vérifier l'existence des tables auth NextAuth
    const expectedTables = ['users', 'accounts', 'sessions', 'verification_tokens'];
    
    for (const tableName of expectedTables) {
      console.log(`🔍 Vérification table ${tableName}...`);
      
      try {
        // Vérifier existence de la table
        const tableExists = await db.execute(sql`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = ${tableName}
          )
        `);

        if (!tableExists[0].exists) {
          diagnostics.issues.push(`Table ${tableName} manquante`);
          diagnostics.recommendations.push(`Créer table ${tableName} avec structure NextAuth`);
          continue;
        }

        // Vérifier la structure des colonnes
        const columns = await db.execute(sql`
          SELECT column_name, data_type, is_nullable, column_default
          FROM information_schema.columns 
          WHERE table_name = ${tableName}
          AND table_schema = 'public'
          ORDER BY ordinal_position
        `);

        // Vérifier les contraintes et index
        const constraints = await db.execute(sql`
          SELECT constraint_name, constraint_type
          FROM information_schema.table_constraints 
          WHERE table_name = ${tableName}
          AND table_schema = 'public'
        `);

        diagnostics.tables[tableName] = {
          exists: true,
          columns: columns.map((col: any) => ({
            name: col.column_name,
            type: col.data_type,
            nullable: col.is_nullable === 'YES',
            default: col.column_default
          })),
          constraints: constraints.map((constraint: any) => ({
            name: constraint.constraint_name,
            type: constraint.constraint_type
          }))
        };

        // Vérifications spécifiques par table
        if (tableName === 'accounts') {
          const accountsColumns = columns.map((col: any) => col.column_name);
          const requiredAccountsColumns = [
            'userId', 'type', 'provider', 'providerAccountId',
            'refresh_token', 'access_token', 'expires_at', 'token_type', 'scope'
          ];
          
          const missingColumns = requiredAccountsColumns.filter(col => !accountsColumns.includes(col));
          if (missingColumns.length > 0) {
            diagnostics.issues.push(`Table accounts: colonnes manquantes: ${missingColumns.join(', ')}`);
            diagnostics.recommendations.push(`Ajouter colonnes manquantes à accounts: ${missingColumns.join(', ')}`);
          }

          // Vérifier clé primaire composite
          const primaryKey = constraints.find((c: any) => c.constraint_type === 'PRIMARY KEY');
          if (!primaryKey) {
            diagnostics.issues.push(`Table accounts: clé primaire manquante`);
            diagnostics.recommendations.push(`Ajouter clé primaire composite (provider, providerAccountId) à accounts`);
          }

          // Compter les comptes existants
          const accountCount = await db.execute(sql`SELECT COUNT(*) as count FROM accounts`);
          diagnostics.tables[tableName].recordCount = Number(accountCount[0].count);
        }

        if (tableName === 'sessions') {
          // Compter les sessions actives
          const sessionCount = await db.execute(sql`
            SELECT COUNT(*) as total, 
                   COUNT(CASE WHEN expires > NOW() THEN 1 END) as active
            FROM sessions
          `);
          diagnostics.tables[tableName].recordCount = Number(sessionCount[0].total);
          diagnostics.tables[tableName].activeCount = Number(sessionCount[0].active);
        }

        if (tableName === 'users') {
          const userCount = await db.execute(sql`SELECT COUNT(*) as count FROM users`);
          diagnostics.tables[tableName].recordCount = Number(userCount[0].count);
        }

        console.log(`✅ Table ${tableName} vérifiée`);

      } catch (error) {
        diagnostics.issues.push(`Erreur vérification table ${tableName}: ${error}`);
        console.error(`❌ Erreur table ${tableName}:`, error);
      }
    }

    // 3. Test de requête NextAuth typique
    console.log("🧪 Test requête NextAuth...");
    try {
      await db.execute(sql`
        SELECT u.id, u.email, a.provider, s."sessionToken"
        FROM users u 
        LEFT JOIN accounts a ON u.id = a."userId" 
        LEFT JOIN sessions s ON u.id = s."userId"
        LIMIT 1
      `);
      console.log("✅ Test requête NextAuth réussi");
    } catch (error) {
      diagnostics.issues.push(`Erreur test requête NextAuth: ${error}`);
      diagnostics.recommendations.push("Vérifier les relations entre tables users/accounts/sessions");
      console.error("❌ Erreur test NextAuth:", error);
    }

    // 4. Recommandations finales
    if (diagnostics.issues.length === 0) {
      diagnostics.recommendations.push("Structure DB auth semble correcte");
    } else {
      diagnostics.recommendations.push("Exécuter les API de correction: /api/admin/db-health-check?autoFix=true");
      diagnostics.recommendations.push("Ou utiliser: /api/admin/fix-accounts-structure");
    }

    console.log(`📊 Diagnostic terminé: ${diagnostics.issues.length} problèmes trouvés`);

    return NextResponse.json({
      success: true,
      message: "Diagnostic des tables auth terminé",
      data: diagnostics
    });

  } catch (error) {
    console.error("❌ Erreur lors du diagnostic:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
} 
 