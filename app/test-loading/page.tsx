import { getSession } from "@/lib/auth";
import { LoadingBarDemo } from "@/components/demo/loading-bar-demo";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

export default async function TestLoadingPage() {
  const session = await getSession();

  return (
    <div className="container py-12">
      {session && (
        <div className="mb-8">
          <Link
            href="/recommendations"
            className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Retour aux recommandations
          </Link>
        </div>
      )}
      
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold tracking-tight mb-4">
            Test de la barre de chargement
          </h1>
          <p className="text-muted-foreground text-lg">
            Démonstration de la barre de chargement en haut de page avec la couleur principale #6236FF
          </p>
        </div>

        <LoadingBarDemo />

        <div className="mt-12 space-y-6">
          <div className="bg-muted/50 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Navigation entre pages</h2>
            <p className="text-muted-foreground mb-4">
              La barre de chargement s'affiche automatiquement lors de la navigation entre les pages. 
              Testez en cliquant sur les liens ci-dessous :
            </p>
            <div className="flex flex-wrap gap-4">
              {session ? (
                <>
                  <Link
                    href="/recommendations"
                    className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                  >
                    Recommandations
                  </Link>
                  <Link
                    href="/wishlist"
                    className="inline-flex items-center px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90 transition-colors"
                  >
                    Ma liste d'envies
                  </Link>
                  <Link
                    href="/collection"
                    className="inline-flex items-center px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90 transition-colors"
                  >
                    Ma collection
                  </Link>
                  <Link
                    href="/account"
                    className="inline-flex items-center px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90 transition-colors"
                  >
                    Mon compte
                  </Link>
                </>
              ) : (
                <>
                  <Link
                    href="/login"
                    className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                  >
                    Se connecter
                  </Link>
                  <Link
                    href="/"
                    className="inline-flex items-center px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90 transition-colors"
                  >
                    Accueil
                  </Link>
                </>
              )}
            </div>
          </div>

          <div className="bg-muted/50 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Fonctionnalités</h2>
            <ul className="space-y-2 text-muted-foreground">
              <li>✅ Barre de chargement automatique lors des navigations</li>
              <li>✅ Couleur principale #6236FF avec effet de lueur</li>
              <li>✅ Z-index élevé (9999) pour être visible au-dessus de tous les éléments</li>
              <li>✅ Hook <code>useLoadingBar()</code> pour contrôle manuel</li>
              <li>✅ Composant <code>LoadingButton</code> avec intégration automatique</li>
              <li>✅ Configuration optimisée pour une expérience fluide</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
