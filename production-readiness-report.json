{"timestamp": "2025-07-21T17:56:04.115Z", "scannedFiles": 236, "issuesFound": 40, "issues": [{"file": "app/actions/user.ts", "description": "Console.log de debug", "matches": 7, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/actions/user.ts", "description": "Ré<PERSON><PERSON><PERSON><PERSON> au sync-logger", "matches": 1, "pattern": "/sync-logger/g"}, {"file": "app/api/admin/check-accounts-structure/route.ts", "description": "Console.log de debug", "matches": 1, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/admin/check-email-config/route.ts", "description": "Console.log de debug", "matches": 1, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/admin/clean-accounts/route.ts", "description": "Console.log de debug", "matches": 3, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/admin/cleanup-orphaned-data/route.ts", "description": "Console.log de debug", "matches": 3, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/admin/db-health-check/route.ts", "description": "Console.log de test", "matches": 1, "pattern": "/console\\.log\\(.*🧪/g"}, {"file": "app/api/admin/db-health-check/route.ts", "description": "Console.log de debug", "matches": 2, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/admin/db-schema-check/route.ts", "description": "Console.log de debug", "matches": 1, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/admin/discogs-sync-stats/route.ts", "description": "Ré<PERSON><PERSON><PERSON><PERSON> au sync-logger", "matches": 1, "pattern": "/sync-logger/g"}, {"file": "app/api/admin/drizzle-push/route.ts", "description": "Console.log de debug", "matches": 2, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/admin/fix-accounts-structure/route.ts", "description": "Console.log de debug", "matches": 2, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/admin/fix-accounts-table/route.ts", "description": "Console.log de debug", "matches": 1, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/admin/fix-column-names/route.ts", "description": "Console.log de debug", "matches": 2, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/admin/full-schema-check/route.ts", "description": "Console.log de debug", "matches": 3, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/admin/init-db/route.ts", "description": "Console.log de debug", "matches": 3, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/admin/init-db-temp/route.ts", "description": "Console.log de debug", "matches": 3, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/admin/migrate-discogs/route.ts", "description": "Console.log de debug", "matches": 1, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/admin/test-follower-email/route.ts", "description": "Console.log de test", "matches": 1, "pattern": "/console\\.log\\(.*🧪/g"}, {"file": "app/api/check-production/route.ts", "description": "Console.log de debug", "matches": 1, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/cron/send-notifications/route.ts", "description": "Console.log de test", "matches": 1, "pattern": "/console\\.log\\(.*🧪/g"}, {"file": "app/api/debug/db-auth-compare/route.ts", "description": "Console.log de test", "matches": 1, "pattern": "/console\\.log\\(.*🧪/g"}, {"file": "app/api/debug/db-auth-compare/route.ts", "description": "Console.log de debug", "matches": 2, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/discogs/callback/route.ts", "description": "Console.log de debug", "matches": 2, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/discogs/connect/route.ts", "description": "Console.log de debug", "matches": 1, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/fix-email-column/route.ts", "description": "Console.log de debug", "matches": 1, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/fix-follow-tables/route.ts", "description": "Console.log de debug", "matches": 1, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/recommendations/filtered/route.ts", "description": "Console.log de debug", "matches": 1, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/test/reset-email-dates/route.ts", "description": "Console.log de test", "matches": 1, "pattern": "/console\\.log\\(.*🧪/g"}, {"file": "components/admin/notification-test-panel.tsx", "description": "Routes API de test", "matches": 4, "pattern": "/\\/api\\/test\\//g"}, {"file": "components/admin/us-1-3-test-panel.tsx", "description": "Routes API de test", "matches": 4, "pattern": "/\\/api\\/test\\//g"}, {"file": "lib/amazon-paapi.ts", "description": "Console.log de debug", "matches": 1, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "lib/amazon-phase1.ts", "description": "Console.log de test", "matches": 1, "pattern": "/console\\.log\\(.*🧪/g"}, {"file": "lib/email.ts", "description": "Console.log de test", "matches": 1, "pattern": "/console\\.log\\(.*🧪/g"}, {"file": "lib/rakuten-auth.ts", "description": "Console.log de test", "matches": 2, "pattern": "/console\\.log\\(.*🧪/g"}, {"file": "lib/rakuten.ts", "description": "Console.log de debug", "matches": 1, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "lib/resend.ts", "description": "Console.log de test", "matches": 1, "pattern": "/console\\.log\\(.*🧪/g"}, {"file": "middleware.ts", "description": "Console.log de debug", "matches": 1, "pattern": "/console\\.log\\(.*🔍/g"}, {"file": "app/api/test", "description": "Route de debug encore présente", "matches": 1, "pattern": "Existence du fichier/dossier"}, {"file": "app/api/debug", "description": "Route de debug non sécurisée (manque la vérification NODE_ENV)", "matches": 1, "pattern": "Sécuris<PERSON> manquante"}], "productionReady": false, "summary": {"criticalIssues": 2, "warningIssues": 34, "infoIssues": 4}}